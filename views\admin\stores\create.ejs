<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1>Create New Store</h1>
      <a href="/admin/stores" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Stores
      </a>
    </div>
  
    <div class="card">
      <div class="card-body">
         <% if (error && error.length > 0) { %>
            <div class="alert alert-danger">
              <%= error %>
            </div>
          <% } %>

          <% if (success && success.length > 0) { %>
            <div class="alert alert-success">
              <%= success %>
            </div>
          <% } %>
        <!-- داخل الـ <form> -->
        <form action="/admin/stores" method="POST" enctype="multipart/form-data">
          <!-- اسم المستخدم -->
          <div class="mb-3">
            <label for="userName" class="form-label">Username</label>
            <input type="text" class="form-control" id="userName" name="userName" required>
          </div>

          <!-- اسم المتجر -->
          <div class="mb-3">
            <label for="name" class="form-label">Store Name</label>
            <input type="text" class="form-control" id="name" name="name" required>
          </div>

          <!-- كلمات السر -->
          <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>

          <!-- الأقسام -->
          <div class="mb-3">
            <label for="categoryIds" class="form-label">Categories</label>
            <select class="form-select" id="categoryIds" name="categoryIds" multiple required>
              <% categories.forEach(cat => { %>
                <option value="<%= cat.id %>"><%= cat.name %></option>
              <% }) %>
            </select>
          </div>
          <!-- العنوان -->
           <input type="text" class="form-control" id="areaName" name="areaName" hidden>
          <div class="mb-3">
            <label for="address" class="form-label">Address</label>
            <div class="input-group">
              <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
              <button type="button" class="btn btn-outline-primary" id="getLocationBtn">
                <i class="fas fa-map-marker-alt"></i> Get Location
              </button>
            </div>
          </div>

          <!-- خريطة -->
          <div class="mb-3">
            <label class="form-label">Store Location on Map</label>
            <div id="map" style="height: 300px; border: 1px solid #ddd; border-radius: 5px;"></div>
          </div>

          <!-- رقم الهاتف -->
          <div class="mb-3">
            <label for="phoneNumber" class="form-label">Phone Number</label>
            <input type="text" class="form-control" id="phoneNumber" name="phoneNumber">
          </div>
          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
          </div>
          
          <div class="mb-3">
              <label for="image" class="form-label">Store Image</label>
              <input type="file" class="form-control" id="image" name="image" accept="image/*">
          </div>
          <!-- ملاحظات -->
          <div class="mb-3">
            <label for="notes" class="form-label">Notes</label>
            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
          </div>

          <!-- ✅ الحقول المخفية للإحداثيات -->
          <input type="hidden" id="latitude" name="latitude">
          <input type="hidden" id="longitude" name="longitude">

          <!-- زر الإرسال -->
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">Create Store</button>
          </div>
        </form>

      </div>
    </div>
  </div>

  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="/libs/leaflet.css" />
  <script src="/libs/leaflet.js"></script>
  <script src="/js/store-map.js"></script>

  <!-- Leaflet JavaScript -->

