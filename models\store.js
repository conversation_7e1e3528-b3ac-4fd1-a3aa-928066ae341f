'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  class Store extends Model {
    static associate(models) {
      // Each store belongs to an Area
      Store.belongsTo(models.Area, {
        foreignKey: 'areaId',
        as: 'area'
      });

      // Many-to-Many: Store <-> Category
      Store.belongsToMany(models.Category, {
        through: 'StoreCategories',
        foreignKey: 'storeId',
        otherKey: 'categoryId',
        as: 'categories'
      });

      // One-to-Many: Store -> Orders
      Store.hasMany(models.Order, {
        foreignKey: 'storeId',
        as: 'orders'
      });

      // One-to-Many: Store -> Products
      Store.hasMany(models.Product, {
        foreignKey: 'storeId',
        as: 'products'
      });

      Store.hasMany(models.DeliveryPerson, {
        as: 'deliveryPeople',
        foreignKey: 'storeId'
      });
      
    }

    // Validate password
    async validatePassword(password) {
      return bcrypt.compare(password, this.password);
    }
  }
  
  Store.init({
    userName: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      set(value) {
        const hashedPassword = bcrypt.hashSync(value, 10);
        this.setDataValue('password', hashedPassword);
      }
    },
    address: DataTypes.TEXT,
    areaId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    phoneNumber: DataTypes.STRING,
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
   
     image: {
      type: DataTypes.STRING,
      allowNull: false
    },
    notes: DataTypes.TEXT,
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'pending'),
      defaultValue: 'active',
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'Store',
  });
  
  return Store;
};
