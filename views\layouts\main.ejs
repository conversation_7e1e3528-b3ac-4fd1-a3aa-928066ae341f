<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> نظام إدارة المتاجر الذكي </title>

    <!-- Meta Tags for SEO -->
    <meta name="description" content="نظام إدارة المتاجر الذكي - منصة شاملة لإدارة المتاجر والطلبات والعملاء">
    <meta name="keywords" content="إدارة متاجر, نظام طلبات, إدارة عملاء, تجارة إلكترونية">
    <meta name="author" content="نظام إدارة المتاجر الذكي">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content=" نظام إدارة المتاجر الذكي ">
    <meta property="og:description" content="منصة شاملة لإدارة المتاجر والطلبات والعملاء">
    <meta property="og:type" content="website">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <!-- Custom CSS الموحد -->
    <link href="/css/main.css" rel="stylesheet">

   
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i>
                نظام إدارة المتاجر
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <% if (locals.session && locals.session.customerId) { %>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="/customers">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="/customers/cart">
                                <i class="fas fa-shopping-cart"></i> السلة
                            </a>
                        </li>
                    <% } else if (locals.session && locals.session.storeId) { %>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="/store/dashboard">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                    <% } else if (locals.session && locals.session.adminId) { %>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                    <% } %>
                </ul>

                <ul class="navbar-nav">
                    <% if (locals.session && locals.session.customerId) { %>
                        <!-- Customer Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link text-white position-relative" href="#" id="customerNotificationDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="customerNotificationCount" style="display: none;">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">الإشعارات</li>
                                <div id="customerNotificationsList"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="/customers/notifications">عرض الكل</a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> حسابي
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/customers/orders">طلباتي</a></li>
                                <li><a class="dropdown-item" href="/customers/profile">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/customers/logout">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <% } else if (locals.session && locals.session.storeId) { %>
                        <!-- Store Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link text-white position-relative" href="#" id="storeNotificationDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="storeNotificationCount" style="display: none;">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">الإشعارات</li>
                                <div id="storeNotificationsList"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="/store/notifications">عرض الكل</a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-store"></i> متجري
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/store/products">المنتجات</a></li>
                                <li><a class="dropdown-item" href="/store/orders">الطلبات</a></li>
                                <li><a class="dropdown-item" href="/store/profile">بيانات المتجر</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/store/logout">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <% } else if (locals.session && locals.session.adminId) { %>
                        <!-- Admin Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link text-white position-relative" href="#" id="adminNotificationDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="adminNotificationCount" style="display: none;">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">الإشعارات</li>
                                <div id="adminNotificationsList"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="/notifications">عرض الكل</a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-shield"></i> الإدارة
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/notifications">الإشعارات</a></li>
                                <li><a class="dropdown-item" href="/admin/auth/change-password">تغيير كلمة المرور</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/admin/auth/logout">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <% } else { %>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="/customers/auth/login">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="/customers/auth/register">إنشاء حساب</a>
                        </li>
                    <% } %>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container-fluid mt-3">
        <% if (locals.success && success.length > 0) { %>
            <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInDown">
                <i class="fas fa-check-circle me-2"></i>
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        <% if (locals.error && error.length > 0) { %>
            <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInDown">
                <i class="fas fa-exclamation-circle me-2"></i>
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
    </div>

    <!-- Main Content -->
    <main>
        <%- body %>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-store"></i> نظام إدارة المتاجر الذكي</h5>
                    <p>منصة شاملة لإدارة المتاجر والطلبات والعملاء</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="/about" class="text-white-50">من نحن</a></li>
                        <li><a href="/contact" class="text-white-50">اتصل بنا</a></li>
                        <li><a href="/terms" class="text-white-50">الشروط والأحكام</a></li>
                        <li><a href="/privacy" class="text-white-50">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <div class="social-links">
                        <a href="#" class="text-white-50 me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-white-50 me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-white-50 me-3"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-white-50"><i class="fab fa-linkedin fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; <%= new Date().getFullYear() %> نظام إدارة المتاجر الذكي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
     <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

   
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
    <script src="/js/main-new.js"></script>

    <script>
        // Hide loading overlay immediately and when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 100);
            }
        });

        // Also hide on window load as backup
        window.addEventListener('load', function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                try {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                } catch (e) {
                    // Fallback if bootstrap is not loaded
                    alert.style.display = 'none';
                }
            });
        }, 5000);

        // Ensure all links work properly
        document.addEventListener('DOMContentLoaded', function() {
            // Remove any loading states from links
            const links = document.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Don't prevent default, let links work normally
                    console.log('Link clicked:', this.href);
                });
            });
        });
    </script>
</body>
</html>
