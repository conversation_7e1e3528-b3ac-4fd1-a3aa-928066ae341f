/**
 * نظام إدارة المتاجر الذكي - ملف CSS موحد ومحسن
 * تم دمج جميع ملفات CSS في ملف واحد شامل
 * نظام ألوان موحد من درجات الأخضر مع تصميم احترافي
 */

/* ========== نظام الألوان الاحترافي ========== */
:root {
  /* الألوان الأساسية - تدرجات الأخضر الاحترافية */
  --primary-50: #f0fdf4;          /* أخضر فاتح جداً للخلفيات */
  --primary-100: #dcfce7;         /* أخضر فاتح للتمييز */
  --primary-200: #bbf7d0;         /* أخضر فاتح للحدود */
  --primary-300: #86efac;         /* أخضر متوسط فاتح */
  --primary-400: #4ade80;         /* أخضر متوسط */
  --primary-500: #B2CD9C;         /* اللون الأساسي - أخضر احترافي */
  --primary-600: #8FB574;         /* أخضر داكن قليلاً */
  --primary-700: #6B9D4F;         /* أخضر داكن */
  --primary-800: #4A7C32;         /* أخضر داكن جداً */
  --primary-900: #365F27;         /* أخضر داكن للنصوص */
  
  /* الألوان الثانوية - تدرجات رمادية دافئة */
  --secondary-50: #fafaf9;        /* رمادي فاتح جداً */
  --secondary-100: #f5f5f4;       /* رمادي فاتح */
  --secondary-200: #e7e5e4;       /* رمادي فاتح متوسط */
  --secondary-300: #d6d3d1;       /* رمادي متوسط فاتح */
  --secondary-400: #a8a29e;       /* رمادي متوسط */
  --secondary-500: #78716c;       /* رمادي أساسي */
  --secondary-600: #57534e;       /* رمادي داكن قليلاً */
  --secondary-700: #44403c;       /* رمادي داكن */
  --secondary-800: #292524;       /* رمادي داكن جداً */
  --secondary-900: #1c1917;       /* رمادي أسود */
  
  /* ألوان الحالة - متناسقة مع النظام */
  --success-50: #f0fdf4;          /* أخضر نجاح فاتح */
  --success-500: #22c55e;         /* أخضر نجاح */
  --success-600: #16a34a;         /* أخضر نجاح داكن */
  --success-700: #15803d;         /* أخضر نجاح داكن جداً */
  
  --warning-50: #fffbeb;          /* برتقالي تحذير فاتح */
  --warning-500: #f59e0b;         /* برتقالي تحذير */
  --warning-600: #d97706;         /* برتقالي تحذير داكن */
  --warning-700: #b45309;         /* برتقالي تحذير داكن جداً */
  
  --danger-50: #fef2f2;           /* أحمر خطر فاتح */
  --danger-500: #ef4444;          /* أحمر خطر */
  --danger-600: #dc2626;          /* أحمر خطر داكن */
  --danger-700: #b91c1c;          /* أحمر خطر داكن جداً */
  
  --info-50: #eff6ff;             /* أزرق معلومات فاتح */
  --info-500: #3b82f6;            /* أزرق معلومات */
  --info-600: #2563eb;            /* أزرق معلومات داكن */
  --info-700: #1d4ed8;            /* أزرق معلومات داكن جداً */
  
  /* ألوان الخلفية */
  --bg-primary: #ffffff;          /* خلفية أساسية بيضاء */
  --bg-secondary: var(--primary-50);  /* خلفية ثانوية خضراء فاتحة */
  --bg-tertiary: var(--secondary-50); /* خلفية ثالثية رمادية */
  --bg-card: #ffffff;             /* خلفية الكروت */
  --bg-dark: var(--secondary-900); /* خلفية داكنة */
  --bg-overlay: rgba(0, 0, 0, 0.5); /* طبقة تراكب */
  
  /* ألوان النصوص */
  --text-primary: var(--secondary-900);    /* نص أساسي داكن */
  --text-secondary: var(--secondary-700);  /* نص ثانوي */
  --text-muted: var(--secondary-500);      /* نص خافت */
  --text-light: var(--secondary-400);      /* نص فاتح */
  --text-white: #ffffff;                   /* نص أبيض */
  --text-on-primary: #ffffff;              /* نص على الخلفية الأساسية */
  
  /* ألوان الحدود */
  --border-light: var(--secondary-200);   /* حدود فاتحة */
  --border-medium: var(--secondary-300);  /* حدود متوسطة */
  --border-dark: var(--secondary-400);    /* حدود داكنة */
  --border-primary: var(--primary-500);   /* حدود أساسية */
  
  /* الظلال الاحترافية */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* المسافات المتدرجة */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem;    /* 2px */
  --spacing-1: 0.25rem;       /* 4px */
  --spacing-1_5: 0.375rem;    /* 6px */
  --spacing-2: 0.5rem;        /* 8px */
  --spacing-2_5: 0.625rem;    /* 10px */
  --spacing-3: 0.75rem;       /* 12px */
  --spacing-3_5: 0.875rem;    /* 14px */
  --spacing-4: 1rem;          /* 16px */
  --spacing-5: 1.25rem;       /* 20px */
  --spacing-6: 1.5rem;        /* 24px */
  --spacing-7: 1.75rem;       /* 28px */
  --spacing-8: 2rem;          /* 32px */
  --spacing-9: 2.25rem;       /* 36px */
  --spacing-10: 2.5rem;       /* 40px */
  --spacing-12: 3rem;         /* 48px */
  --spacing-16: 4rem;         /* 64px */
  --spacing-20: 5rem;         /* 80px */
  --spacing-24: 6rem;         /* 96px */
  
  /* نصف الأقطار */
  --radius-none: 0;
  --radius-sm: 0.125rem;      /* 2px */
  --radius-base: 0.25rem;     /* 4px */
  --radius-md: 0.375rem;      /* 6px */
  --radius-lg: 0.5rem;        /* 8px */
  --radius-xl: 0.75rem;       /* 12px */
  --radius-2xl: 1rem;         /* 16px */
  --radius-3xl: 1.5rem;       /* 24px */
  --radius-full: 9999px;      /* دائري كامل */
  
  /* الخطوط */
  --font-family-arabic: 'Cairo', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  --font-family-english: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', 'Liberation Mono', monospace;
  
  /* أحجام الخطوط */
  --text-xs: 0.75rem;         /* 12px */
  --text-sm: 0.875rem;        /* 14px */
  --text-base: 1rem;          /* 16px */
  --text-lg: 1.125rem;        /* 18px */
  --text-xl: 1.25rem;         /* 20px */
  --text-2xl: 1.5rem;         /* 24px */
  --text-3xl: 1.875rem;       /* 30px */
  --text-4xl: 2.25rem;        /* 36px */
  --text-5xl: 3rem;           /* 48px */
  --text-6xl: 3.75rem;        /* 60px */
  
  /* أوزان الخطوط */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  /* ارتفاع الأسطر */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* الانتقالات */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-colors: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-opacity: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ========== إعدادات عامة ========== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-arabic);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%);
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* دعم اللغة الإنجليزية */
[lang="en"], 
.en,
.english {
  direction: ltr;
  text-align: left;
  font-family: var(--font-family-english);
}

/* إعدادات الصور */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* إعدادات القوائم */
ul, ol {
  list-style: none;
}

/* إعدادات الجداول */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* إعدادات الأزرار والنماذج */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: var(--leading-normal);
  margin: 0;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

/* إخفاء شاشة التحميل */
.loading-overlay {
  display: none !important;
}

/* ========== العناوين ========== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-6);
}

h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-5);
}

h3 {
  font-size: var(--text-2xl);
  margin-bottom: var(--spacing-4);
}

h4 {
  font-size: var(--text-xl);
  margin-bottom: var(--spacing-3);
}

h5 {
  font-size: var(--text-lg);
  margin-bottom: var(--spacing-3);
}

h6 {
  font-size: var(--text-base);
  margin-bottom: var(--spacing-2);
}

/* ========== النصوص ========== */
p {
  margin-bottom: var(--spacing-4);
  line-height: var(--leading-relaxed);
}

.lead {
  font-size: var(--text-lg);
  font-weight: var(--font-light);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

.small {
  font-size: var(--text-sm);
}

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

/* ========== الروابط ========== */
a {
  color: var(--primary-500);
  text-decoration: none;
  transition: var(--transition-colors);
  cursor: pointer;
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

a:active {
  color: var(--primary-800);
}

/* روابط خاصة */
.link-primary { color: var(--primary-500); }
.link-primary:hover { color: var(--primary-700); }

.link-secondary { color: var(--text-secondary); }
.link-secondary:hover { color: var(--text-primary); }

.link-muted { color: var(--text-muted); }
.link-muted:hover { color: var(--text-secondary); }

/* ========== الأزرار ========== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-none);
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-all);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  z-index: var(--z-10);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* تأثير الموجة */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

/* أنواع الأزرار */
.btn-primary {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-color: var(--primary-500);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  border-color: var(--primary-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
  color: var(--text-primary);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-success {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  border-color: var(--success-500);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
  border-color: var(--success-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-warning {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  border-color: var(--warning-500);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
  border-color: var(--warning-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-danger {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
  border-color: var(--danger-500);
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
  border-color: var(--danger-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-info {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
  border-color: var(--info-500);
  box-shadow: var(--shadow-sm);
}

.btn-info:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%);
  border-color: var(--info-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* أحجام الأزرار */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--text-xs);
  border-radius: var(--radius-md);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--text-xl);
  border-radius: var(--radius-2xl);
}

/* أزرار بعرض كامل */
.btn-block {
  width: 100%;
  justify-content: center;
}

/* أزرار الخطوط الخارجية */
.btn-outline-primary {
  color: var(--primary-500);
  background-color: transparent;
  border-color: var(--primary-500);
}

.btn-outline-primary:hover:not(:disabled) {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-color: var(--primary-500);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-outline-secondary {
  color: var(--text-secondary);
  background-color: transparent;
  border-color: var(--border-medium);
}

.btn-outline-secondary:hover:not(:disabled) {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* أزرار الأشباح */
.btn-ghost {
  color: var(--primary-500);
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  color: var(--primary-700);
  background-color: var(--primary-50);
  text-decoration: none;
}

/* أزرار الروابط */
.btn-link {
  color: var(--primary-500);
  background-color: transparent;
  border-color: transparent;
  text-decoration: underline;
  padding: 0;
  border-radius: 0;
}

.btn-link:hover:not(:disabled) {
  color: var(--primary-700);
  text-decoration: underline;
}

/* ========== الكروت ========== */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition-all);
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--border-medium);
}

.card-elevated {
  box-shadow: var(--shadow-md);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--text-white);
  border-bottom: none;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

.card-header-light {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--spacing-6);
}

.card-body-sm {
  padding: var(--spacing-4);
}

.card-body-lg {
  padding: var(--spacing-8);
}

.card-footer {
  padding: var(--spacing-5) var(--spacing-6);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
  color: var(--text-secondary);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
  line-height: var(--leading-tight);
}

.card-subtitle {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-muted);
  margin-bottom: var(--spacing-4);
}

.card-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
  line-height: var(--leading-relaxed);
}

.card-text:last-child {
  margin-bottom: 0;
}

/* ========== النماذج ========== */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  font-weight: var(--font-medium);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.form-label.required::after {
  content: ' *';
  color: var(--danger-500);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
  background-color: var(--bg-primary);
}

.form-control:hover:not(:focus):not(:disabled) {
  border-color: var(--border-medium);
}

.form-control:disabled,
.form-control[readonly] {
  background-color: var(--bg-tertiary);
  border-color: var(--border-light);
  opacity: 0.7;
  cursor: not-allowed;
}

.form-control.is-valid {
  border-color: var(--success-500);
}

.form-control.is-valid:focus {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px var(--success-50);
}

.form-control.is-invalid {
  border-color: var(--danger-500);
}

.form-control.is-invalid:focus {
  border-color: var(--danger-500);
  box-shadow: 0 0 0 3px var(--danger-50);
}

/* أحجام النماذج */
.form-control-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.form-control-lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

/* منطقة النص */
textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

/* القوائم المنسدلة */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-left: var(--spacing-10);
}

[dir="ltr"] .form-select {
  background-position: right var(--spacing-3) center;
  padding-right: var(--spacing-10);
  padding-left: var(--spacing-4);
}

/* مربعات الاختيار والراديو */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin: 0;
  border: 2px solid var(--border-medium);
  border-radius: var(--radius-base);
  background-color: var(--bg-primary);
  transition: var(--transition-all);
  cursor: pointer;
}

.form-check-input:checked {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
}

.form-check-input[type="radio"] {
  border-radius: var(--radius-full);
}

.form-check-label {
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  user-select: none;
}

/* نص المساعدة */
.form-text {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-1);
  line-height: var(--leading-normal);
}

.form-text.text-success {
  color: var(--success-600);
}

.form-text.text-danger {
  color: var(--danger-600);
}

/* ========== التنبيهات ========== */
.alert {
  padding: var(--spacing-4) var(--spacing-5);
  margin-bottom: var(--spacing-5);
  border: 1px solid transparent;
  border-radius: var(--radius-xl);
  font-size: var(--text-sm);
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  box-shadow: var(--shadow-sm);
}

.alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-1);
}

.alert-dismissible {
  padding-left: var(--spacing-12);
}

.alert-dismissible .btn-close {
  position: absolute;
  top: var(--spacing-4);
  left: var(--spacing-4);
  padding: var(--spacing-1);
  background: none;
  border: none;
  font-size: var(--text-lg);
  cursor: pointer;
  opacity: 0.7;
  transition: var(--transition-opacity);
}

.alert-dismissible .btn-close:hover {
  opacity: 1;
}

/* أنواع التنبيهات */
.alert-success {
  color: var(--success-700);
  background: linear-gradient(135deg, var(--success-50) 0%, #f0fdf4 100%);
  border-color: var(--success-200);
}

.alert-success .alert-icon {
  color: var(--success-600);
}

.alert-warning {
  color: var(--warning-700);
  background: linear-gradient(135deg, var(--warning-50) 0%, #fffbeb 100%);
  border-color: var(--warning-200);
}

.alert-warning .alert-icon {
  color: var(--warning-600);
}

.alert-danger {
  color: var(--danger-700);
  background: linear-gradient(135deg, var(--danger-50) 0%, #fef2f2 100%);
  border-color: var(--danger-200);
}

.alert-danger .alert-icon {
  color: var(--danger-600);
}

.alert-info {
  color: var(--info-700);
  background: linear-gradient(135deg, var(--info-50) 0%, #eff6ff 100%);
  border-color: var(--info-200);
}

.alert-info .alert-icon {
  color: var(--info-600);
}

.alert-primary {
  color: var(--primary-700);
  background: linear-gradient(135deg, var(--primary-50) 0%, #f0fdf4 100%);
  border-color: var(--primary-200);
}

.alert-primary .alert-icon {
  color: var(--primary-600);
}

/* ========== الجداول ========== */
.table-container {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.table {
  width: 100%;
  margin-bottom: 0;
  background-color: transparent;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th,
.table td {
  padding: var(--spacing-4) var(--spacing-5);
  text-align: right;
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
  line-height: var(--leading-normal);
}

.table th {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  font-weight: var(--font-semibold);
  color: var(--text-white);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: none;
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

.table tbody tr {
  transition: var(--transition-all);
}

.table tbody tr:hover {
  background-color: var(--primary-50);
  transform: scale(1.01);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.table-striped tbody tr:nth-child(odd) {
  background-color: var(--bg-tertiary);
}

.table-striped tbody tr:nth-child(odd):hover {
  background-color: var(--primary-50);
}

.table-bordered {
  border: 1px solid var(--border-light);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-light);
}

.table-sm th,
.table-sm td {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-xs);
}

.table-lg th,
.table-lg td {
  padding: var(--spacing-5) var(--spacing-6);
  font-size: var(--text-base);
}

.table-responsive {
  overflow-x: auto;
  border-radius: var(--radius-xl);
  -webkit-overflow-scrolling: touch;
}

/* حالات الجدول */
.table .table-active {
  background-color: var(--primary-100);
}

.table .table-primary {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.table .table-success {
  background-color: var(--success-50);
  color: var(--success-700);
}

.table .table-warning {
  background-color: var(--warning-50);
  color: var(--warning-700);
}

.table .table-danger {
  background-color: var(--danger-50);
  color: var(--danger-700);
}

.table .table-info {
  background-color: var(--info-50);
  color: var(--info-700);
}

/* أعمدة قابلة للترتيب */
.table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-left: var(--spacing-8);
}

.table th.sortable::after {
  content: '↕';
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.5;
  font-size: var(--text-xs);
}

.table th.sortable.asc::after {
  content: '↑';
  opacity: 1;
}

.table th.sortable.desc::after {
  content: '↓';
  opacity: 1;
}

.table th.sortable:hover::after {
  opacity: 1;
}

/* ========== نظام الإشعارات المحسن ========== */
#notifications-container {
  position: fixed;
  top: var(--spacing-5);
  z-index: var(--z-modal);
  max-width: 400px;
  pointer-events: none;
}

.notification-item {
  pointer-events: auto;
  margin-bottom: var(--spacing-3);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: none;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.notification-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.notification-item.alert-success::before {
  background: linear-gradient(90deg, var(--success-500) 0%, var(--success-600) 100%);
}

.notification-item.alert-warning::before {
  background: linear-gradient(90deg, var(--warning-500) 0%, var(--warning-600) 100%);
}

.notification-item.alert-danger::before,
.notification-item.alert-error::before {
  background: linear-gradient(90deg, var(--danger-500) 0%, var(--danger-600) 100%);
}

.notification-item.alert-info::before {
  background: linear-gradient(90deg, var(--info-500) 0%, var(--info-600) 100%);
}

.notification-item .alert-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  color: var(--text-white);
  margin-left: var(--spacing-3);
}

.notification-item.alert-success .alert-icon {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
}

.notification-item.alert-warning .alert-icon {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
}

.notification-item.alert-danger .alert-icon,
.notification-item.alert-error .alert-icon {
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
}

.notification-item.alert-info .alert-icon {
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
}

.notification-item.alert-primary .alert-icon {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.notification-item .alert-content {
  flex: 1;
  padding: var(--spacing-1) 0;
}

.notification-item .alert-title {
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-1);
  color: var(--text-primary);
}

.notification-item .alert-message {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

.notification-item .btn-close {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  background: rgba(0, 0, 0, 0.1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition-all);
  opacity: 0.7;
}

.notification-item .btn-close:hover {
  background: rgba(0, 0, 0, 0.2);
  opacity: 1;
  transform: scale(1.1);
}

.notification-item .btn-close:active {
  transform: scale(0.95);
}

/* ========== الأدوات المساعدة ========== */

/* محاذاة النص */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* ألوان النص */
.text-primary { color: var(--primary-500) !important; }
.text-primary-light { color: var(--primary-300) !important; }
.text-primary-dark { color: var(--primary-700) !important; }

.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--text-light) !important; }
.text-dark { color: var(--text-primary) !important; }
.text-white { color: var(--text-white) !important; }

.text-success { color: var(--success-500) !important; }
.text-success-light { color: var(--success-300) !important; }
.text-success-dark { color: var(--success-700) !important; }

.text-warning { color: var(--warning-500) !important; }
.text-warning-light { color: var(--warning-300) !important; }
.text-warning-dark { color: var(--warning-700) !important; }

.text-danger { color: var(--danger-500) !important; }
.text-danger-light { color: var(--danger-300) !important; }
.text-danger-dark { color: var(--danger-700) !important; }

.text-info { color: var(--info-500) !important; }
.text-info-light { color: var(--info-300) !important; }
.text-info-dark { color: var(--info-700) !important; }

/* أوزان النص */
.font-thin { font-weight: var(--font-thin) !important; }
.font-light { font-weight: var(--font-light) !important; }
.font-normal { font-weight: var(--font-normal) !important; }
.font-medium { font-weight: var(--font-medium) !important; }
.font-semibold { font-weight: var(--font-semibold) !important; }
.font-bold { font-weight: var(--font-bold) !important; }
.font-extrabold { font-weight: var(--font-extrabold) !important; }
.font-black { font-weight: var(--font-black) !important; }

/* ألوان الخلفية */
.bg-primary { background-color: var(--primary-500) !important; color: var(--text-white) !important; }
.bg-primary-light { background-color: var(--primary-100) !important; color: var(--primary-700) !important; }
.bg-primary-dark { background-color: var(--primary-700) !important; color: var(--text-white) !important; }

.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }
.bg-white { background-color: var(--bg-primary) !important; }
.bg-dark { background-color: var(--bg-dark) !important; color: var(--text-white) !important; }

.bg-success { background-color: var(--success-500) !important; color: var(--text-white) !important; }
.bg-success-light { background-color: var(--success-50) !important; color: var(--success-700) !important; }

.bg-warning { background-color: var(--warning-500) !important; color: var(--text-white) !important; }
.bg-warning-light { background-color: var(--warning-50) !important; color: var(--warning-700) !important; }

.bg-danger { background-color: var(--danger-500) !important; color: var(--text-white) !important; }
.bg-danger-light { background-color: var(--danger-50) !important; color: var(--danger-700) !important; }

.bg-info { background-color: var(--info-500) !important; color: var(--text-white) !important; }
.bg-info-light { background-color: var(--info-50) !important; color: var(--info-700) !important; }

/* العرض */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-col { flex-direction: column !important; }
.flex-col-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

/* الأبعاد */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-min { width: min-content !important; }
.w-max { width: max-content !important; }
.w-fit { width: fit-content !important; }

.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-min { height: min-content !important; }
.h-max { height: max-content !important; }
.h-fit { height: fit-content !important; }

/* المسافات */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }
.m-10 { margin: var(--spacing-10) !important; }
.m-12 { margin: var(--spacing-12) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }

.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }

.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.px-5 { padding-left: var(--spacing-5) !important; padding-right: var(--spacing-5) !important; }

.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }

/* الحدود */
.border { border: 1px solid var(--border-light) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--border-light) !important; }
.border-r { border-right: 1px solid var(--border-light) !important; }
.border-b { border-bottom: 1px solid var(--border-light) !important; }
.border-l { border-left: 1px solid var(--border-light) !important; }

.border-primary { border-color: var(--primary-500) !important; }
.border-secondary { border-color: var(--border-medium) !important; }
.border-success { border-color: var(--success-500) !important; }
.border-warning { border-color: var(--warning-500) !important; }
.border-danger { border-color: var(--danger-500) !important; }
.border-info { border-color: var(--info-500) !important; }

/* نصف الأقطار */
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius-base) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-3xl { border-radius: var(--radius-3xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* الظلال */
.shadow-none { box-shadow: none !important; }
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }
.shadow-inner { box-shadow: var(--shadow-inner) !important; }

/* ========== الاستجابة للشاشات ========== */

/* الشاشات الصغيرة (الهواتف) */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .navbar-toggle {
    display: block;
  }

  .navbar-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-4);
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .navbar-nav.show {
    display: flex;
  }

  .sidebar {
    transform: translateX(100%);
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    z-index: var(--z-modal);
    transition: var(--transition-transform);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .table-responsive {
    font-size: var(--text-xs);
  }

  .table th,
  .table td {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--text-sm);
  }

  .btn-lg {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--text-base);
  }

  .card-body {
    padding: var(--spacing-4);
  }

  .card-header,
  .card-footer {
    padding: var(--spacing-4);
  }

  .form-control {
    padding: var(--spacing-3);
  }

  /* تصغير العناوين */
  h1 { font-size: var(--text-3xl); }
  h2 { font-size: var(--text-2xl); }
  h3 { font-size: var(--text-xl); }
  h4 { font-size: var(--text-lg); }
  h5 { font-size: var(--text-base); }
  h6 { font-size: var(--text-sm); }

  /* إخفاء عناصر في الشاشات الصغيرة */
  .d-sm-none { display: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

/* الشاشات المتوسطة (الأجهزة اللوحية) */
@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding: 0 var(--spacing-5);
  }

  .sidebar {
    width: 240px;
  }

  .table-responsive {
    font-size: var(--text-sm);
  }

  .card-body {
    padding: var(--spacing-5);
  }

  /* عرض/إخفاء للشاشات المتوسطة */
  .d-md-none { display: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

/* الشاشات الكبيرة (أجهزة الكمبيوتر) */
@media (min-width: 1025px) {
  .container {
    padding: 0 var(--spacing-6);
  }

  .sidebar {
    width: 280px;
  }

  /* عرض/إخفاء للشاشات الكبيرة */
  .d-lg-none { display: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}

/* ========== الرسوم المتحركة ========== */

/* تأثيرات الحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.4s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.4s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

/* تأثيرات التمرير */
.hover-lift {
  transition: var(--transition-all);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: var(--transition-transform);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: var(--transition-transform);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* تأثيرات الضغط */
.press-effect {
  transition: var(--transition-transform);
}

.press-effect:active {
  transform: scale(0.95);
}

/* ========== تحسينات إضافية ========== */

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

.smooth-scroll {
  scroll-behavior: smooth;
}

/* تحسينات إمكانية الوصول */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.focus-visible:focus,
.focus-ring:focus {
  outline: 2px solid var(--primary-500) !important;
  outline-offset: 2px !important;
  border-radius: var(--radius-sm) !important;
}

/* فئات مساعدة إضافية */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-wrap {
  white-space: normal !important;
}

.vertical-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.full-height {
  min-height: 100vh;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
}

/* تحسينات للتفاعل */
.interactive {
  cursor: pointer;
  transition: var(--transition-all);
}

.interactive:hover {
  opacity: 0.8;
}

.interactive:active {
  transform: scale(0.98);
}

/* تحسينات للطباعة */
@media print {
  .no-print { display: none !important; }
  .sidebar, .navbar { display: none !important; }
  .container { max-width: none !important; padding: 0 !important; }
  #notifications-container { display: none !important; }
}

/* تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }

  .notification-item {
    animation: none !important;
  }
}

/* نهاية ملف CSS الموحد */
