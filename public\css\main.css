/**
 * نظام إدارة المتاجر الذكي - تصميم احترافي متكامل
 * نظام ألوان موحد من درجات الأخضر مع تناسق مثالي
 * تصميم متجاوب ومتوافق مع النصوص العربية
 */

/* ========== نظام الألوان الاحترافي ========== */
:root {
  /* الألوان الأساسية - تدرجات الأخضر الاحترافية */
  --primary-50: #f0fdf4;          /* أخضر فاتح جداً للخلفيات */
  --primary-100: #dcfce7;         /* أخضر فاتح للتمييز */
  --primary-200: #bbf7d0;         /* أخضر فاتح للحدود */
  --primary-300: #86efac;         /* أخضر متوسط فاتح */
  --primary-400: #4ade80;         /* أخضر متوسط */
  --primary-500: #B2CD9C;         /* اللون الأساسي - أخضر احترافي */
  --primary-600: #8FB574;         /* أخضر داكن قليلاً */
  --primary-700: #6B9D4F;         /* أخضر داكن */
  --primary-800: #4A7C32;         /* أخضر داكن جداً */
  --primary-900: #365F27;         /* أخضر داكن للنصوص */

  /* الألوان الثانوية - تدرجات رمادية دافئة */
  --secondary-50: #fafaf9;        /* رمادي فاتح جداً */
  --secondary-100: #f5f5f4;       /* رمادي فاتح */
  --secondary-200: #e7e5e4;       /* رمادي فاتح متوسط */
  --secondary-300: #d6d3d1;       /* رمادي متوسط فاتح */
  --secondary-400: #a8a29e;       /* رمادي متوسط */
  --secondary-500: #78716c;       /* رمادي أساسي */
  --secondary-600: #57534e;       /* رمادي داكن قليلاً */
  --secondary-700: #44403c;       /* رمادي داكن */
  --secondary-800: #292524;       /* رمادي داكن جداً */
  --secondary-900: #1c1917;       /* رمادي أسود */

  /* ألوان الحالة - متناسقة مع النظام */
  --success-50: #f0fdf4;          /* أخضر نجاح فاتح */
  --success-500: #22c55e;         /* أخضر نجاح */
  --success-600: #16a34a;         /* أخضر نجاح داكن */
  --success-700: #15803d;         /* أخضر نجاح داكن جداً */

  --warning-50: #fffbeb;          /* برتقالي تحذير فاتح */
  --warning-500: #f59e0b;         /* برتقالي تحذير */
  --warning-600: #d97706;         /* برتقالي تحذير داكن */
  --warning-700: #b45309;         /* برتقالي تحذير داكن جداً */

  --danger-50: #fef2f2;           /* أحمر خطر فاتح */
  --danger-500: #ef4444;          /* أحمر خطر */
  --danger-600: #dc2626;          /* أحمر خطر داكن */
  --danger-700: #b91c1c;          /* أحمر خطر داكن جداً */

  --info-50: #eff6ff;             /* أزرق معلومات فاتح */
  --info-500: #3b82f6;            /* أزرق معلومات */
  --info-600: #2563eb;            /* أزرق معلومات داكن */
  --info-700: #1d4ed8;            /* أزرق معلومات داكن جداً */

  /* ألوان الخلفية */
  --bg-primary: #ffffff;          /* خلفية أساسية بيضاء */
  --bg-secondary: var(--primary-50);  /* خلفية ثانوية خضراء فاتحة */
  --bg-tertiary: var(--secondary-50); /* خلفية ثالثية رمادية */
  --bg-card: #ffffff;             /* خلفية الكروت */
  --bg-dark: var(--secondary-900); /* خلفية داكنة */
  --bg-overlay: rgba(0, 0, 0, 0.5); /* طبقة تراكب */

  /* ألوان النصوص */
  --text-primary: var(--secondary-900);    /* نص أساسي داكن */
  --text-secondary: var(--secondary-700);  /* نص ثانوي */
  --text-muted: var(--secondary-500);      /* نص خافت */
  --text-light: var(--secondary-400);      /* نص فاتح */
  --text-white: #ffffff;                   /* نص أبيض */
  --text-on-primary: #ffffff;              /* نص على الخلفية الأساسية */

  /* ألوان الحدود */
  --border-light: var(--secondary-200);   /* حدود فاتحة */
  --border-medium: var(--secondary-300);  /* حدود متوسطة */
  --border-dark: var(--secondary-400);    /* حدود داكنة */
  --border-primary: var(--primary-500);   /* حدود أساسية */

  /* الظلال الاحترافية */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* المسافات المتدرجة */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem;    /* 2px */
  --spacing-1: 0.25rem;       /* 4px */
  --spacing-1_5: 0.375rem;    /* 6px */
  --spacing-2: 0.5rem;        /* 8px */
  --spacing-2_5: 0.625rem;    /* 10px */
  --spacing-3: 0.75rem;       /* 12px */
  --spacing-3_5: 0.875rem;    /* 14px */
  --spacing-4: 1rem;          /* 16px */
  --spacing-5: 1.25rem;       /* 20px */
  --spacing-6: 1.5rem;        /* 24px */
  --spacing-7: 1.75rem;       /* 28px */
  --spacing-8: 2rem;          /* 32px */
  --spacing-9: 2.25rem;       /* 36px */
  --spacing-10: 2.5rem;       /* 40px */
  --spacing-12: 3rem;         /* 48px */
  --spacing-16: 4rem;         /* 64px */
  --spacing-20: 5rem;         /* 80px */
  --spacing-24: 6rem;         /* 96px */

  /* نصف الأقطار */
  --radius-none: 0;
  --radius-sm: 0.125rem;      /* 2px */
  --radius-base: 0.25rem;     /* 4px */
  --radius-md: 0.375rem;      /* 6px */
  --radius-lg: 0.5rem;        /* 8px */
  --radius-xl: 0.75rem;       /* 12px */
  --radius-2xl: 1rem;         /* 16px */
  --radius-3xl: 1.5rem;       /* 24px */
  --radius-full: 9999px;      /* دائري كامل */

  /* الخطوط */
  --font-family-arabic: 'Cairo', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  --font-family-english: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', 'Liberation Mono', monospace;

  /* أحجام الخطوط */
  --text-xs: 0.75rem;         /* 12px */
  --text-sm: 0.875rem;        /* 14px */
  --text-base: 1rem;          /* 16px */
  --text-lg: 1.125rem;        /* 18px */
  --text-xl: 1.25rem;         /* 20px */
  --text-2xl: 1.5rem;         /* 24px */
  --text-3xl: 1.875rem;       /* 30px */
  --text-4xl: 2.25rem;        /* 36px */
  --text-5xl: 3rem;           /* 48px */
  --text-6xl: 3.75rem;        /* 60px */

  /* أوزان الخطوط */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* ارتفاع الأسطر */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* الانتقالات */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-colors: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-opacity: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-Index */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ========== إعدادات عامة ========== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-arabic);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* دعم اللغة الإنجليزية */
[lang="en"],
.en,
.english {
  direction: ltr;
  text-align: left;
  font-family: var(--font-family-english);
}

/* إعدادات الصور */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* إعدادات القوائم */
ul, ol {
  list-style: none;
}

/* إعدادات الجداول */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* إعدادات الأزرار والنماذج */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: var(--leading-normal);
  margin: 0;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

/* ========== العناوين ========== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-6);
}

h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-5);
}

h3 {
  font-size: var(--text-2xl);
  margin-bottom: var(--spacing-4);
}

h4 {
  font-size: var(--text-xl);
  margin-bottom: var(--spacing-3);
}

h5 {
  font-size: var(--text-lg);
  margin-bottom: var(--spacing-3);
}

h6 {
  font-size: var(--text-base);
  margin-bottom: var(--spacing-2);
}

/* ========== النصوص ========== */
p {
  margin-bottom: var(--spacing-4);
  line-height: var(--leading-relaxed);
}

.lead {
  font-size: var(--text-lg);
  font-weight: var(--font-light);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

.small {
  font-size: var(--text-sm);
}

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

/* ========== الروابط ========== */
a {
  color: var(--primary-500);
  text-decoration: none;
  transition: var(--transition-colors);
  cursor: pointer;
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

a:active {
  color: var(--primary-800);
}

/* روابط خاصة */
.link-primary { color: var(--primary-500); }
.link-primary:hover { color: var(--primary-700); }

.link-secondary { color: var(--text-secondary); }
.link-secondary:hover { color: var(--text-primary); }

.link-muted { color: var(--text-muted); }
.link-muted:hover { color: var(--text-secondary); }

/* ========== الأزرار ========== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-none);
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-all);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  z-index: var(--z-10);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* تأثير الموجة */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

/* أنواع الأزرار */
.btn-primary {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-color: var(--primary-500);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  border-color: var(--primary-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
  color: var(--text-primary);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-success {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  border-color: var(--success-500);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
  border-color: var(--success-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-warning {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  border-color: var(--warning-500);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
  border-color: var(--warning-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-danger {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
  border-color: var(--danger-500);
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
  border-color: var(--danger-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-info {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
  border-color: var(--info-500);
  box-shadow: var(--shadow-sm);
}

.btn-info:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%);
  border-color: var(--info-600);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* أحجام الأزرار */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--text-xs);
  border-radius: var(--radius-md);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--text-xl);
  border-radius: var(--radius-2xl);
}

/* أزرار بعرض كامل */
.btn-block {
  width: 100%;
  justify-content: center;
}

/* أزرار الخطوط الخارجية */
.btn-outline-primary {
  color: var(--primary-500);
  background-color: transparent;
  border-color: var(--primary-500);
}

.btn-outline-primary:hover:not(:disabled) {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-color: var(--primary-500);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-outline-secondary {
  color: var(--text-secondary);
  background-color: transparent;
  border-color: var(--border-medium);
}

.btn-outline-secondary:hover:not(:disabled) {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
  text-decoration: none;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* أزرار الأشباح */
.btn-ghost {
  color: var(--primary-500);
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  color: var(--primary-700);
  background-color: var(--primary-50);
  text-decoration: none;
}

/* أزرار الروابط */
.btn-link {
  color: var(--primary-500);
  background-color: transparent;
  border-color: transparent;
  text-decoration: underline;
  padding: 0;
  border-radius: 0;
}

.btn-link:hover:not(:disabled) {
  color: var(--primary-700);
  text-decoration: underline;
}

/* ========== الكروت ========== */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition-all);
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--border-medium);
}

.card-elevated {
  box-shadow: var(--shadow-md);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--text-white);
  border-bottom: none;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

.card-header-light {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--spacing-6);
}

.card-body-sm {
  padding: var(--spacing-4);
}

.card-body-lg {
  padding: var(--spacing-8);
}

.card-footer {
  padding: var(--spacing-5) var(--spacing-6);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
  color: var(--text-secondary);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
  line-height: var(--leading-tight);
}

.card-subtitle {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-muted);
  margin-bottom: var(--spacing-4);
}

.card-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
  line-height: var(--leading-relaxed);
}

.card-text:last-child {
  margin-bottom: 0;
}

/* كروت خاصة */
.card-primary {
  border-color: var(--primary-500);
}

.card-primary .card-header {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--text-white);
}

.card-success {
  border-color: var(--success-500);
}

.card-success .card-header {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  color: var(--text-white);
}

.card-warning {
  border-color: var(--warning-500);
}

.card-warning .card-header {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  color: var(--text-white);
}

.card-danger {
  border-color: var(--danger-500);
}

.card-danger .card-header {
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
  color: var(--text-white);
}

/* كروت الإحصائيات */
.stats-card {
  background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-tertiary) 100%);
  border: none;
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  text-align: center;
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.stats-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.stats-card-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
  font-size: var(--text-2xl);
  color: var(--text-white);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.stats-card-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.stats-card-label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

/* ========== النماذج ========== */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  font-weight: var(--font-medium);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.form-label.required::after {
  content: ' *';
  color: var(--danger-500);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
  background-color: var(--bg-primary);
}

.form-control:hover:not(:focus):not(:disabled) {
  border-color: var(--border-medium);
}

.form-control:disabled,
.form-control[readonly] {
  background-color: var(--bg-tertiary);
  border-color: var(--border-light);
  opacity: 0.7;
  cursor: not-allowed;
}

.form-control.is-valid {
  border-color: var(--success-500);
}

.form-control.is-valid:focus {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px var(--success-50);
}

.form-control.is-invalid {
  border-color: var(--danger-500);
}

.form-control.is-invalid:focus {
  border-color: var(--danger-500);
  box-shadow: 0 0 0 3px var(--danger-50);
}

/* أحجام النماذج */
.form-control-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.form-control-lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

/* منطقة النص */
textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

/* القوائم المنسدلة */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-left: var(--spacing-10);
}

[dir="ltr"] .form-select {
  background-position: right var(--spacing-3) center;
  padding-right: var(--spacing-10);
  padding-left: var(--spacing-4);
}

/* مربعات الاختيار والراديو */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin: 0;
  border: 2px solid var(--border-medium);
  border-radius: var(--radius-base);
  background-color: var(--bg-primary);
  transition: var(--transition-all);
  cursor: pointer;
}

.form-check-input:checked {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
}

.form-check-input[type="radio"] {
  border-radius: var(--radius-full);
}

.form-check-label {
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  user-select: none;
}

/* نص المساعدة */
.form-text {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-1);
  line-height: var(--leading-normal);
}

.form-text.text-success {
  color: var(--success-600);
}

.form-text.text-danger {
  color: var(--danger-600);
}

/* مجموعات الإدخال */
.input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
}

.input-group .form-control {
  border-radius: 0;
  border-left: 0;
}

.input-group .form-control:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
  border-left: 2px solid var(--border-light);
}

.input-group .form-control:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.input-group .input-group-text:not(:first-child) {
  border-left: 0;
  border-radius: 0;
}

.input-group .input-group-text:not(:last-child) {
  border-right: 0;
  border-radius: 0;
}

.input-group .input-group-text:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.input-group .input-group-text:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

/* ========== التنبيهات ========== */
.alert {
  padding: var(--spacing-4) var(--spacing-5);
  margin-bottom: var(--spacing-5);
  border: 1px solid transparent;
  border-radius: var(--radius-xl);
  font-size: var(--text-sm);
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  box-shadow: var(--shadow-sm);
}

.alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-1);
}

.alert-dismissible {
  padding-left: var(--spacing-12);
}

.alert-dismissible .btn-close {
  position: absolute;
  top: var(--spacing-4);
  left: var(--spacing-4);
  padding: var(--spacing-1);
  background: none;
  border: none;
  font-size: var(--text-lg);
  cursor: pointer;
  opacity: 0.7;
  transition: var(--transition-opacity);
}

.alert-dismissible .btn-close:hover {
  opacity: 1;
}

/* أنواع التنبيهات */
.alert-success {
  color: var(--success-700);
  background: linear-gradient(135deg, var(--success-50) 0%, #f0fdf4 100%);
  border-color: var(--success-200);
}

.alert-success .alert-icon {
  color: var(--success-600);
}

.alert-warning {
  color: var(--warning-700);
  background: linear-gradient(135deg, var(--warning-50) 0%, #fffbeb 100%);
  border-color: var(--warning-200);
}

.alert-warning .alert-icon {
  color: var(--warning-600);
}

.alert-danger {
  color: var(--danger-700);
  background: linear-gradient(135deg, var(--danger-50) 0%, #fef2f2 100%);
  border-color: var(--danger-200);
}

.alert-danger .alert-icon {
  color: var(--danger-600);
}

.alert-info {
  color: var(--info-700);
  background: linear-gradient(135deg, var(--info-50) 0%, #eff6ff 100%);
  border-color: var(--info-200);
}

.alert-info .alert-icon {
  color: var(--info-600);
}

.alert-primary {
  color: var(--primary-700);
  background: linear-gradient(135deg, var(--primary-50) 0%, #f0fdf4 100%);
  border-color: var(--primary-200);
}

.alert-primary .alert-icon {
  color: var(--primary-600);
}

/* تنبيهات صلبة */
.alert-solid-success {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  border-color: var(--success-500);
}

.alert-solid-warning {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  border-color: var(--warning-500);
}

.alert-solid-danger {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
  border-color: var(--danger-500);
}

.alert-solid-info {
  color: var(--text-white);
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
  border-color: var(--info-500);
}

/* ========== الجداول ========== */
.table-container {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.table {
  width: 100%;
  margin-bottom: 0;
  background-color: transparent;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th,
.table td {
  padding: var(--spacing-4) var(--spacing-5);
  text-align: right;
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
  line-height: var(--leading-normal);
}

.table th {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  font-weight: var(--font-semibold);
  color: var(--text-white);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: none;
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

.table tbody tr {
  transition: var(--transition-all);
}

.table tbody tr:hover {
  background-color: var(--primary-50);
  transform: scale(1.01);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.table-striped tbody tr:nth-child(odd) {
  background-color: var(--bg-tertiary);
}

.table-striped tbody tr:nth-child(odd):hover {
  background-color: var(--primary-50);
}

.table-bordered {
  border: 1px solid var(--border-light);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-light);
}

.table-sm th,
.table-sm td {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-xs);
}

.table-lg th,
.table-lg td {
  padding: var(--spacing-5) var(--spacing-6);
  font-size: var(--text-base);
}

.table-responsive {
  overflow-x: auto;
  border-radius: var(--radius-xl);
  -webkit-overflow-scrolling: touch;
}

/* حالات الجدول */
.table .table-active {
  background-color: var(--primary-100);
}

.table .table-primary {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.table .table-success {
  background-color: var(--success-50);
  color: var(--success-700);
}

.table .table-warning {
  background-color: var(--warning-50);
  color: var(--warning-700);
}

.table .table-danger {
  background-color: var(--danger-50);
  color: var(--danger-700);
}

.table .table-info {
  background-color: var(--info-50);
  color: var(--info-700);
}

/* أعمدة قابلة للترتيب */
.table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-left: var(--spacing-8);
}

.table th.sortable::after {
  content: '↕';
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.5;
  font-size: var(--text-xs);
}

.table th.sortable.asc::after {
  content: '↑';
  opacity: 1;
}

.table th.sortable.desc::after {
  content: '↓';
  opacity: 1;
}

.table th.sortable:hover::after {
  opacity: 1;
}

/* ========== الشريط الجانبي ========== */
.sidebar {
  background: linear-gradient(180deg, var(--secondary-900) 0%, var(--secondary-800) 100%);
  color: var(--text-white);
  min-height: 100vh;
  padding: var(--spacing-6);
  box-shadow: var(--shadow-2xl);
  position: relative;
  overflow: hidden;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.sidebar-brand {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  transition: var(--transition-colors);
}

.sidebar-brand:hover {
  color: var(--primary-300);
  text-decoration: none;
}

.sidebar-brand-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
}

.sidebar-nav {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar-nav-item {
  margin-bottom: var(--spacing-1);
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  position: relative;
  overflow: hidden;
}

.sidebar-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--primary-500);
  transform: scaleY(0);
  transition: var(--transition-transform);
}

.sidebar-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  text-decoration: none;
  transform: translateX(4px);
}

.sidebar-nav-link:hover::before {
  transform: scaleY(1);
}

.sidebar-nav-link.active {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--text-white);
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.sidebar-nav-link.active::before {
  transform: scaleY(1);
  background: var(--text-white);
}

.sidebar-nav-icon {
  width: 20px;
  height: 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.sidebar-nav-text {
  flex: 1;
}

.sidebar-nav-badge {
  background: var(--danger-500);
  color: var(--text-white);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--spacing-0_5) var(--spacing-2);
  border-radius: var(--radius-full);
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* مجموعات الشريط الجانبي */
.sidebar-section {
  margin-bottom: var(--spacing-6);
}

.sidebar-section-title {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-3);
  padding: 0 var(--spacing-4);
}

/* الشريط الجانبي المصغر */
.sidebar-mini .sidebar-nav-text,
.sidebar-mini .sidebar-nav-badge,
.sidebar-mini .sidebar-brand-text,
.sidebar-mini .sidebar-section-title {
  display: none;
}

.sidebar-mini .sidebar {
  width: 70px;
}

.sidebar-mini .sidebar-nav-link {
  justify-content: center;
  padding: var(--spacing-3);
}

/* ========== الشريط العلوي ========== */
.navbar {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-4) 0;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(10px);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.navbar-brand {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-500);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  transition: var(--transition-colors);
}

.navbar-brand:hover {
  color: var(--primary-700);
  text-decoration: none;
}

.navbar-brand-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: var(--text-lg);
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav-item {
  position: relative;
}

.navbar-nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.navbar-nav-link:hover {
  color: var(--primary-500);
  background-color: var(--primary-50);
  text-decoration: none;
}

.navbar-nav-link.active {
  color: var(--primary-600);
  background-color: var(--primary-100);
  text-decoration: none;
}

/* قائمة منسدلة في الشريط العلوي */
.navbar-dropdown {
  position: relative;
}

.navbar-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  min-width: 200px;
  padding: var(--spacing-2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-all);
  z-index: var(--z-dropdown);
}

.navbar-dropdown:hover .navbar-dropdown-menu,
.navbar-dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.navbar-dropdown-item {
  display: block;
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: var(--transition-colors);
  font-size: var(--text-sm);
}

.navbar-dropdown-item:hover {
  color: var(--primary-500);
  background-color: var(--primary-50);
  text-decoration: none;
}

.navbar-dropdown-divider {
  height: 1px;
  background: var(--border-light);
  margin: var(--spacing-2) 0;
}

/* شارة الإشعارات */
.navbar-notification {
  position: relative;
}

.navbar-notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--danger-500);
  color: var(--text-white);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: var(--spacing-0_5) var(--spacing-1_5);
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* زر القائمة للجوال */
.navbar-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--text-xl);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  transition: var(--transition-colors);
}

.navbar-toggle:hover {
  color: var(--primary-500);
  background-color: var(--primary-50);
}

/* ========== الشبكة ========== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-6);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--spacing-3));
}

.col {
  flex: 1;
  padding: 0 var(--spacing-3);
  min-width: 0;
}

/* نظام الشبكة 12 عمود */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; padding: 0 var(--spacing-3); }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; padding: 0 var(--spacing-3); }
.col-3 { flex: 0 0 25%; max-width: 25%; padding: 0 var(--spacing-3); }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; padding: 0 var(--spacing-3); }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; padding: 0 var(--spacing-3); }
.col-6 { flex: 0 0 50%; max-width: 50%; padding: 0 var(--spacing-3); }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; padding: 0 var(--spacing-3); }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; padding: 0 var(--spacing-3); }
.col-9 { flex: 0 0 75%; max-width: 75%; padding: 0 var(--spacing-3); }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; padding: 0 var(--spacing-3); }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; padding: 0 var(--spacing-3); }
.col-12 { flex: 0 0 100%; max-width: 100%; padding: 0 var(--spacing-3); }

/* شبكة متجاوبة للشاشات المتوسطة */
@media (min-width: 768px) {
  .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* شبكة متجاوبة للشاشات الكبيرة */
@media (min-width: 1024px) {
  .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* فجوات الشبكة */
.row.g-0 { margin: 0; }
.row.g-0 > * { padding: 0; }

.row.g-1 { margin: 0 calc(-1 * var(--spacing-1)); }
.row.g-1 > * { padding: 0 var(--spacing-1); }

.row.g-2 { margin: 0 calc(-1 * var(--spacing-2)); }
.row.g-2 > * { padding: 0 var(--spacing-2); }

.row.g-3 { margin: 0 calc(-1 * var(--spacing-3)); }
.row.g-3 > * { padding: 0 var(--spacing-3); }

.row.g-4 { margin: 0 calc(-1 * var(--spacing-4)); }
.row.g-4 > * { padding: 0 var(--spacing-4); }

.row.g-5 { margin: 0 calc(-1 * var(--spacing-5)); }
.row.g-5 > * { padding: 0 var(--spacing-5); }

/* ========== المسافات ========== */
/* الهوامش */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }
.m-10 { margin: var(--spacing-10) !important; }
.m-12 { margin: var(--spacing-12) !important; }

/* الهوامش العلوية */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }
.mt-10 { margin-top: var(--spacing-10) !important; }
.mt-12 { margin-top: var(--spacing-12) !important; }

/* الهوامش السفلية */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }
.mb-10 { margin-bottom: var(--spacing-10) !important; }
.mb-12 { margin-bottom: var(--spacing-12) !important; }

/* الهوامش اليمنى واليسرى */
.mr-0, .me-0 { margin-right: 0 !important; }
.mr-1, .me-1 { margin-right: var(--spacing-1) !important; }
.mr-2, .me-2 { margin-right: var(--spacing-2) !important; }
.mr-3, .me-3 { margin-right: var(--spacing-3) !important; }
.mr-4, .me-4 { margin-right: var(--spacing-4) !important; }
.mr-5, .me-5 { margin-right: var(--spacing-5) !important; }

.ml-0, .ms-0 { margin-left: 0 !important; }
.ml-1, .ms-1 { margin-left: var(--spacing-1) !important; }
.ml-2, .ms-2 { margin-left: var(--spacing-2) !important; }
.ml-3, .ms-3 { margin-left: var(--spacing-3) !important; }
.ml-4, .ms-4 { margin-left: var(--spacing-4) !important; }
.ml-5, .ms-5 { margin-left: var(--spacing-5) !important; }

/* الهوامش الأفقية والعمودية */
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-1) !important; margin-right: var(--spacing-1) !important; }
.mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
.mx-3 { margin-left: var(--spacing-3) !important; margin-right: var(--spacing-3) !important; }
.mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
.mx-5 { margin-left: var(--spacing-5) !important; margin-right: var(--spacing-5) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-1) !important; margin-bottom: var(--spacing-1) !important; }
.my-2 { margin-top: var(--spacing-2) !important; margin-bottom: var(--spacing-2) !important; }
.my-3 { margin-top: var(--spacing-3) !important; margin-bottom: var(--spacing-3) !important; }
.my-4 { margin-top: var(--spacing-4) !important; margin-bottom: var(--spacing-4) !important; }
.my-5 { margin-top: var(--spacing-5) !important; margin-bottom: var(--spacing-5) !important; }

/* الحشو */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }
.p-10 { padding: var(--spacing-10) !important; }
.p-12 { padding: var(--spacing-12) !important; }

/* الحشو العلوي */
.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-1) !important; }
.pt-2 { padding-top: var(--spacing-2) !important; }
.pt-3 { padding-top: var(--spacing-3) !important; }
.pt-4 { padding-top: var(--spacing-4) !important; }
.pt-5 { padding-top: var(--spacing-5) !important; }
.pt-6 { padding-top: var(--spacing-6) !important; }
.pt-8 { padding-top: var(--spacing-8) !important; }
.pt-10 { padding-top: var(--spacing-10) !important; }
.pt-12 { padding-top: var(--spacing-12) !important; }

/* الحشو السفلي */
.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-1) !important; }
.pb-2 { padding-bottom: var(--spacing-2) !important; }
.pb-3 { padding-bottom: var(--spacing-3) !important; }
.pb-4 { padding-bottom: var(--spacing-4) !important; }
.pb-5 { padding-bottom: var(--spacing-5) !important; }
.pb-6 { padding-bottom: var(--spacing-6) !important; }
.pb-8 { padding-bottom: var(--spacing-8) !important; }
.pb-10 { padding-bottom: var(--spacing-10) !important; }
.pb-12 { padding-bottom: var(--spacing-12) !important; }

/* الحشو الأفقي والعمودي */
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-1) !important; padding-right: var(--spacing-1) !important; }
.px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.px-5 { padding-left: var(--spacing-5) !important; padding-right: var(--spacing-5) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-1) !important; padding-bottom: var(--spacing-1) !important; }
.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }
.py-5 { padding-top: var(--spacing-5) !important; padding-bottom: var(--spacing-5) !important; }

/* ========== الأدوات المساعدة ========== */

/* محاذاة النص */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* ألوان النص */
.text-primary { color: var(--primary-500) !important; }
.text-primary-light { color: var(--primary-300) !important; }
.text-primary-dark { color: var(--primary-700) !important; }

.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--text-light) !important; }
.text-dark { color: var(--text-primary) !important; }
.text-white { color: var(--text-white) !important; }

.text-success { color: var(--success-500) !important; }
.text-success-light { color: var(--success-300) !important; }
.text-success-dark { color: var(--success-700) !important; }

.text-warning { color: var(--warning-500) !important; }
.text-warning-light { color: var(--warning-300) !important; }
.text-warning-dark { color: var(--warning-700) !important; }

.text-danger { color: var(--danger-500) !important; }
.text-danger-light { color: var(--danger-300) !important; }
.text-danger-dark { color: var(--danger-700) !important; }

.text-info { color: var(--info-500) !important; }
.text-info-light { color: var(--info-300) !important; }
.text-info-dark { color: var(--info-700) !important; }

/* أوزان النص */
.font-thin { font-weight: var(--font-thin) !important; }
.font-light { font-weight: var(--font-light) !important; }
.font-normal { font-weight: var(--font-normal) !important; }
.font-medium { font-weight: var(--font-medium) !important; }
.font-semibold { font-weight: var(--font-semibold) !important; }
.font-bold { font-weight: var(--font-bold) !important; }
.font-extrabold { font-weight: var(--font-extrabold) !important; }
.font-black { font-weight: var(--font-black) !important; }

/* ألوان الخلفية */
.bg-primary { background-color: var(--primary-500) !important; color: var(--text-white) !important; }
.bg-primary-light { background-color: var(--primary-100) !important; color: var(--primary-700) !important; }
.bg-primary-dark { background-color: var(--primary-700) !important; color: var(--text-white) !important; }

.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }
.bg-white { background-color: var(--bg-primary) !important; }
.bg-dark { background-color: var(--bg-dark) !important; color: var(--text-white) !important; }

.bg-success { background-color: var(--success-500) !important; color: var(--text-white) !important; }
.bg-success-light { background-color: var(--success-50) !important; color: var(--success-700) !important; }

.bg-warning { background-color: var(--warning-500) !important; color: var(--text-white) !important; }
.bg-warning-light { background-color: var(--warning-50) !important; color: var(--warning-700) !important; }

.bg-danger { background-color: var(--danger-500) !important; color: var(--text-white) !important; }
.bg-danger-light { background-color: var(--danger-50) !important; color: var(--danger-700) !important; }

.bg-info { background-color: var(--info-500) !important; color: var(--text-white) !important; }
.bg-info-light { background-color: var(--info-50) !important; color: var(--info-700) !important; }

/* العرض */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-col { flex-direction: column !important; }
.flex-col-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

/* الأبعاد */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-min { width: min-content !important; }
.w-max { width: max-content !important; }
.w-fit { width: fit-content !important; }

.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-min { height: min-content !important; }
.h-max { height: max-content !important; }
.h-fit { height: fit-content !important; }

/* الحدود */
.border { border: 1px solid var(--border-light) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--border-light) !important; }
.border-r { border-right: 1px solid var(--border-light) !important; }
.border-b { border-bottom: 1px solid var(--border-light) !important; }
.border-l { border-left: 1px solid var(--border-light) !important; }

.border-primary { border-color: var(--primary-500) !important; }
.border-secondary { border-color: var(--border-medium) !important; }
.border-success { border-color: var(--success-500) !important; }
.border-warning { border-color: var(--warning-500) !important; }
.border-danger { border-color: var(--danger-500) !important; }
.border-info { border-color: var(--info-500) !important; }

/* نصف الأقطار */
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius-base) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-3xl { border-radius: var(--radius-3xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* الظلال */
.shadow-none { box-shadow: none !important; }
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }
.shadow-inner { box-shadow: var(--shadow-inner) !important; }

/* الشفافية */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* الموضع */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* الفيض */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* المؤشر */
.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-help { cursor: help !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* اختيار المستخدم */
.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }
.select-auto { user-select: auto !important; }

/* ========== الاستجابة للشاشات ========== */

/* الشاشات الصغيرة (الهواتف) */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .navbar-toggle {
    display: block;
  }

  .navbar-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-4);
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .navbar-nav.show {
    display: flex;
  }

  .sidebar {
    transform: translateX(100%);
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    z-index: var(--z-modal);
    transition: var(--transition-transform);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .table-responsive {
    font-size: var(--text-xs);
  }

  .table th,
  .table td {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--text-sm);
  }

  .btn-lg {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--text-base);
  }

  .card-body {
    padding: var(--spacing-4);
  }

  .card-header,
  .card-footer {
    padding: var(--spacing-4);
  }

  .form-control {
    padding: var(--spacing-3);
  }

  .stats-card {
    padding: var(--spacing-4);
  }

  .stats-card-icon {
    width: 48px;
    height: 48px;
    font-size: var(--text-xl);
  }

  .stats-card-value {
    font-size: var(--text-2xl);
  }

  /* تصغير العناوين */
  h1 { font-size: var(--text-3xl); }
  h2 { font-size: var(--text-2xl); }
  h3 { font-size: var(--text-xl); }
  h4 { font-size: var(--text-lg); }
  h5 { font-size: var(--text-base); }
  h6 { font-size: var(--text-sm); }

  /* إخفاء عناصر في الشاشات الصغيرة */
  .d-sm-none { display: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

/* الشاشات المتوسطة (الأجهزة اللوحية) */
@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding: 0 var(--spacing-5);
  }

  .sidebar {
    width: 240px;
  }

  .table-responsive {
    font-size: var(--text-sm);
  }

  .card-body {
    padding: var(--spacing-5);
  }

  .stats-card-icon {
    width: 56px;
    height: 56px;
  }

  /* عرض/إخفاء للشاشات المتوسطة */
  .d-md-none { display: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

/* الشاشات الكبيرة (أجهزة الكمبيوتر) */
@media (min-width: 1025px) {
  .container {
    padding: 0 var(--spacing-6);
  }

  .sidebar {
    width: 280px;
  }

  /* عرض/إخفاء للشاشات الكبيرة */
  .d-lg-none { display: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}

/* الشاشات الكبيرة جداً */
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }

  .sidebar {
    width: 320px;
  }

  /* عرض/إخفاء للشاشات الكبيرة جداً */
  .d-xl-none { display: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
}

/* ========== تحسينات إضافية ========== */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* رسوم متحركة إضافية */
.slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.4s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* تأثيرات التمرير */
.hover-scale:hover {
  transform: scale(1.05);
  transition: var(--transition-transform);
}

.press-effect:active {
  transform: scale(0.95);
  transition: var(--transition-transform);
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* تحسينات إمكانية الوصول */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.focus-ring:focus {
  outline: 2px solid var(--primary-500) !important;
  outline-offset: 2px !important;
}

/* فئات مساعدة */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vertical-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.full-height {
  min-height: 100vh;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

/* تحسينات للطباعة */
@media print {
  .no-print { display: none !important; }
  .sidebar, .navbar { display: none !important; }
  .container { max-width: none !important; padding: 0 !important; }
}

/* تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}

        /* تحديث متغيرات الألوان لتتماشى مع النظام الجديد */
        :root {
            --primary-color: #B2CD9C;
            --primary-dark: #8FB574;
            --primary-light: #C5D9B1;
            --secondary-color: #64748b;
            --success-color: #22c55e;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --light-color: #f8fafc;
            --dark-color: #0f172a;
            --border-radius: 1rem;
            --box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
            --box-shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: var(--dark-color);
        }

        /* Loading Spinner */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Navigation */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--box-shadow-lg);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-decoration: none;
        }

        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* Sidebar */
        .sidebar {
            min-height: calc(100vh - 76px);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--box-shadow);
            position: sticky;
            top: 76px;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.15);
            transform: translateX(-5px);
            box-shadow: var(--box-shadow);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin: 1.5rem;
            padding: 2rem;
            min-height: calc(100vh - 152px);
            color: var(--dark-color);
        }

        /* Text Colors */
        .text-dark {
            color: var(--dark-color) !important;
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--dark-color);
        }

        p, span, div {
            color: inherit;
        }

        /* Container fixes */
        .container, .container-fluid {
            color: var(--dark-color);
        }

        /* Cards */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--box-shadow-lg);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            padding: 1.25rem;
            font-weight: 600;
        }

        /* Buttons */
        .btn {
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        /* Tables */
        .table-container {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .table {
            margin: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1.25rem;
            position: relative;
        }

        .table tbody tr {
            transition: var(--transition);
        }

        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }

        .table td {
            padding: 1rem 1.25rem;
            vertical-align: middle;
            border-color: rgba(0,0,0,0.05);
        }

        /* Badges */
        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 500;
        }

        /* Notifications */
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Forms */
        .form-control, .form-select {
            border-radius: 0.75rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* Alerts */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 76px;
                left: -100%;
                width: 280px;
                z-index: 1000;
                transition: var(--transition);
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin: 1rem;
                padding: 1rem;
            }

            .table-responsive {
                border-radius: var(--border-radius);
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            .main-content {
                background: #2d3748;
                color: white;
            }

            .card {
                background: #4a5568;
                color: white;
            }

            .table {
                color: white;
            }

            .table tbody tr:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }