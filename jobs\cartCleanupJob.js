const cron = require('node-cron');
const CartService = require('../services/CartService');

/**
 * مهمة تنظيف السلات المنتهية الصلاحية
 * تعمل كل ساعة لحذف العناصر المنتهية الصلاحية
 */
class CartCleanupJob {
    
    constructor() {
        this.isRunning = false;
        this.schedule = '0 * * * *'; // كل ساعة
    }

    /**
     * بدء تشغيل المهمة
     */
    start() {
        console.log('🧹 بدء تشغيل مهمة تنظيف السلة...');
        
        cron.schedule(this.schedule, async () => {
            if (this.isRunning) {
                console.log('⏳ مهمة تنظيف السلة قيد التشغيل بالفعل، تخطي...');
                return;
            }

            this.isRunning = true;
            
            try {
                console.log('🧹 بدء تنظيف السلات المنتهية الصلاحية...');
                const deletedCount = await CartService.cleanExpiredCarts();
                
                if (deletedCount > 0) {
                    console.log(`✅ تم حذف ${deletedCount} عنصر منتهي الصلاحية من السلة`);
                } else {
                    console.log('✅ لا توجد عناصر منتهية الصلاحية للحذف');
                }
                
            } catch (error) {
                console.error('❌ خطأ في تنظيف السلة:', error);
            } finally {
                this.isRunning = false;
            }
        }, {
            scheduled: true,
            timezone: "Asia/Riyadh"
        });

        console.log('✅ تم تشغيل مهمة تنظيف السلة بنجاح');
    }

    /**
     * إيقاف المهمة
     */
    stop() {
        console.log('🛑 إيقاف مهمة تنظيف السلة...');
        // يمكن إضافة منطق إيقاف المهمة هنا إذا لزم الأمر
    }

    /**
     * تشغيل التنظيف يدوياً
     */
    async runManually() {
        if (this.isRunning) {
            throw new Error('المهمة قيد التشغيل بالفعل');
        }

        this.isRunning = true;
        
        try {
            console.log('🧹 تشغيل تنظيف السلة يدوياً...');
            const deletedCount = await CartService.cleanExpiredCarts();
            console.log(`✅ تم حذف ${deletedCount} عنصر منتهي الصلاحية`);
            return deletedCount;
            
        } catch (error) {
            console.error('❌ خطأ في التنظيف اليدوي:', error);
            throw error;
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * الحصول على حالة المهمة
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            schedule: this.schedule,
            nextRun: this.getNextRunTime()
        };
    }

    /**
     * الحصول على وقت التشغيل التالي
     */
    getNextRunTime() {
        // حساب الوقت التالي للتشغيل
        const now = new Date();
        const nextHour = new Date(now);
        nextHour.setHours(now.getHours() + 1, 0, 0, 0);
        return nextHour;
    }
}

module.exports = new CartCleanupJob();
