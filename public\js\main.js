/**
 * نظام إدارة المتاجر الذكي - JavaScript محدث
 * تحسينات التفاعل والتجربة المستخدم مع النظام الجديد
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام
    initializeSystem();

    // تهيئة التأثيرات
    initializeAnimations();

    // تهيئة النماذج
    initializeForms();

    // تهيئة الجداول
    initializeTables();

    // تهيئة الإشعارات
    initializeNotifications();

});

/**
 * تهيئة النظام العام
 */
function initializeSystem() {
    // إخفاء شاشة التحميل
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 300);
        }, 1000);
    }

    // تهيئة الشريط الجانبي للجوال
    initializeMobileSidebar();

    // تهيئة القوائم المنسدلة
    initializeDropdowns();

    // تهيئة التنبيهات التلقائية
    initializeAutoAlerts();
}

/**
 * تهيئة الشريط الجانبي للجوال
 */
function initializeMobileSidebar() {
    const sidebarToggle = document.querySelector('.navbar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            if (overlay) {
                overlay.classList.toggle('show');
            }
        });

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            });
        }
    }
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.navbar-dropdown');

    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.navbar-dropdown-menu');

        dropdown.addEventListener('mouseenter', function() {
            if (menu) menu.classList.add('show');
        });

        dropdown.addEventListener('mouseleave', function() {
            if (menu) menu.classList.remove('show');
        });
    });
}

/**
 * تهيئة التنبيهات التلقائية
 */
function initializeAutoAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-dismissible')) {
                try {
                    const closeBtn = alert.querySelector('.btn-close');
                    if (closeBtn) {
                        closeBtn.click();
                    } else {
                        alert.style.display = 'none';
                    }
                } catch (e) {
                    alert.style.display = 'none';
                }
            }
        });
    }, 5000);

    // تأكيد الحذف
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-btn') || e.target.closest('.delete-btn')) {
            if (!confirm('هل أنت متأكد من أنك تريد حذف هذا العنصر؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
}

/**
 * تهيئة التأثيرات والحركات
 */
function initializeAnimations() {
    // تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // مراقبة الكروت والعناصر
    const animatedElements = document.querySelectorAll('.card, .stats-card, .alert');
    animatedElements.forEach(el => {
        observer.observe(el);
    });

    // تأثير النقر على الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الضغط
            this.classList.add('press-effect');
            setTimeout(() => {
                this.classList.remove('press-effect');
            }, 150);
        });
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // تحسين تجربة النماذج
    const formControls = document.querySelectorAll('.form-control');

    formControls.forEach(control => {
        // تأثير التركيز
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            validateField(this);
        });

        // التحقق أثناء الكتابة
        control.addEventListener('input', function() {
            clearTimeout(this.validateTimeout);
            this.validateTimeout = setTimeout(() => {
                validateField(this);
            }, 500);
        });
    });

    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> جاري المعالجة...';

                // إعادة تفعيل الزر بعد 5 ثوان في حالة عدم انتقال الصفحة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 5000);
            }
        });
    });
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');

    // إزالة الفئات السابقة
    field.classList.remove('is-valid', 'is-invalid');

    // التحقق من الحقول المطلوبة
    if (required && !value) {
        field.classList.add('is-invalid');
        return false;
    }

    // التحقق من البريد الإلكتروني
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            field.classList.add('is-invalid');
            return false;
        }
    }

    // إذا وصلنا هنا، فالحقل صحيح
    if (value) {
        field.classList.add('is-valid');
    }

    return true;
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    const tables = document.querySelectorAll('.table');

    tables.forEach(table => {
        // إضافة إمكانية الترتيب
        const sortableHeaders = table.querySelectorAll('th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                sortTable(table, this);
            });
        });
    });
}

/**
 * ترتيب الجدول
 */
function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentElement.children).indexOf(header);
    const isAscending = !header.classList.contains('asc');

    // إزالة فئات الترتيب من جميع الرؤوس
    table.querySelectorAll('th.sortable').forEach(th => {
        th.classList.remove('asc', 'desc');
    });

    // إضافة فئة الترتيب للرأس الحالي
    header.classList.add(isAscending ? 'asc' : 'desc');

    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();

        // محاولة التحويل إلى رقم
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        } else {
            return isAscending ?
                aValue.localeCompare(bValue, 'ar') :
                bValue.localeCompare(aValue, 'ar');
        }
    });

    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * تهيئة نظام الإشعارات
 */
function initializeNotifications() {
    // إنشاء حاوي الإشعارات
    if (!document.getElementById('notifications-container')) {
        const container = document.createElement('div');
        container.id = 'notifications-container';
        container.className = 'position-fixed';
        container.style.cssText = 'top: 20px; left: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(container);
    }
}

/**
 * إظهار إشعار
 */
function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notifications-container');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show mb-3`;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    container.appendChild(notification);

    // إزالة الإشعار تلقائياً
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, duration);
    }
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'danger': 'times-circle',
        'info': 'info-circle',
        'primary': 'bell'
    };
    return icons[type] || 'bell';
}

// وظائف مساعدة عامة
window.SmartStore = {
    showNotification: showNotification,

    // عرض رسالة تأكيد
    confirm: function(message, callback) {
        if (confirm(message || 'هل أنت متأكد؟')) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },

    // إعادة تحميل الصفحة
    reload: function() {
        window.location.reload();
    },

    // الانتقال لصفحة أخرى
    redirect: function(url) {
        window.location.href = url;
    }
};

