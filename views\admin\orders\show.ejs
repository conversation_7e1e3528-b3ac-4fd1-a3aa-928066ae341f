<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>الطلبات</h1>
    </div>

    <% if (locals.message) { %>
        <div class="alert alert-info"><%= message %></div>
    <% } %>

    <% if (orders && orders.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>الزبون</th>
                        <th>المتجر</th>
                        <th>السعر الإجمالي</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <% orders.forEach(order => { %>
                        <tr>
                            <td><%= order.id %></td>
                            <td><%= order.customer ? order.customer.name : '<PERSON><PERSON><PERSON> متوفر' %></td>
                            <td><%= order.store ? order.store.name : '<PERSON>ي<PERSON> متوفر' %></td>
                            <td>$<%= (order.totalPrice || 0).toFixed(2) %></td>
                            <td>
                                <span class="badge bg-<%= !order.status ? 'secondary' :
                                                        order.status === 'pending' ? 'warning' : 
                                                        order.status === 'completed' ? 'success' : 
                                                        order.status === 'cancelled' ? 'danger' : 'secondary' %>">
                                    <%= order.status === 'pending' ? 'قيد المعالجة' :
                                         order.status === 'completed' ? 'مكتمل' :
                                         order.status === 'cancelled' ? 'ملغي' :
                                         'غير معروف' %>
                                </span>
                            </td>
                            <td><%= order.createdAt ? new Date(order.createdAt).toLocaleDateString('ar-EG') : 'غير متوفر' %></td>
                            <td class="action-buttons">
                                <a href="orders/<%= order.id %>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> عرض
                                </a>       
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="alert alert-info">لا توجد طلبات.</div>
    <% } %>
</div>
