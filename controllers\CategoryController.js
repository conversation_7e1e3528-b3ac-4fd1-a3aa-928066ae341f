const { Category, Store } = require('../models');
const path = require('path');

exports.index = async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;
  
    try {
      const { count, rows: categories } = await Category.findAndCountAll({
        include: [{ model: Store, as: 'stores' }],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('admin/categories/index', {
        categories,
        currentPage: page,
        totalPages
      });
    } catch (error) {
      console.error('Error fetching categories:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch categories' } });
    }
  };
  

exports.createForm = (req, res) => {
  res.render('admin/categories/create');
};

exports.create = async (req, res) => {
  try {
    console.log(req.body);
    console.log(req.files);
    if (!req.files || !req.files.image) {
      req.flash('error', 'Please upload an image.');
      return res.redirect('/admin/categories/create');
    }
    
    const image = req.files.image;

    // تحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(image.mimetype)) {
      req.flash('error', 'Invalid image format.');
      return res.redirect('/admin/categories/create');
    }

    // إعداد اسم ومسار حفظ الصورة
    const fileName = `${Date.now()}-${image.name}`;
    const uploadPath = path.join(__dirname, '../public/uploads', fileName);

    // حفظ الصورة
    await image.mv(uploadPath);

    // أضف اسم الصورة إلى بيانات الـ category
    req.body.image = fileName;

    if (req.body.status === 'نَشِط') {
      req.body.status = 'active';
    } else if (req.body.status === 'غير نشط') {
      req.body.status = 'inactive';
    }

    // إنشاء التصنيف مع بيانات الصورة
    await Category.create(req.body);

    req.flash('success', 'Category created');
    res.redirect('/admin/categories');
  } catch (err) {
    console.error('Error creating category:', err);
    req.flash('error', 'Error creating category');
    res.redirect('/admin/categories/create');
  }
};
exports.editForm = async (req, res) => {
    try {
        const category = await Category.findByPk(req.params.id);
    
        if (!category) {
            return res.status(404).render('error', {
                error: { status: 404, message: 'category not found' }
            });
        }
    
        res.render('admin/categories/edit', {category});
    } catch (error) {
        console.error('Error loading edit category:', error);
        res.status(500).render('error', { error: { message: 'Unable to load category data' } });
    }
};

exports.update = async (req, res) => {
  try {
        const cat = await Category.findByPk(req.params.id);
        if (!cat) {
            return res.status(404).render('error', {
                error: { status: 404, message: 'Category not found' }
            });
        }

       await Category.update(req.body, {
            where: { id: req.params.id }});
        req.flash('success', 'Category updated successfully');
        res.redirect('/admin/categories');
    } catch (error) {
        console.error('Error updating categories:', error);
        res.status(500).render('error', { error: { message: 'Unable to update category' } });
    }
};

// حذف 
exports.delete = async (req, res) => {
    try {
        const category = await Category.findByPk(req.params.id);
        if (!category) {
            return res.status(404).render('error', {
                error: { status: 404, message: 'Category not found' }
            });
        }

        await category.destroy();
        req.flash('success', 'Category deleted successfully');
        res.redirect('/admin/categories');
    } catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).render('error', { error: { message: 'Unable to delete category' } });
    }
}