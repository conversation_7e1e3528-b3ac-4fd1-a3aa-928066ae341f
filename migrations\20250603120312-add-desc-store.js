'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Stores', 'image', {
      type: Sequelize.STRING,
      allowNull: true
    });
    await queryInterface.addColumn('Stores', 'description', {
      type: Sequelize.TEXT,
      allowNull: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('stores', 'image');
    await queryInterface.removeColumn('Stores', 'description');
  }
};
