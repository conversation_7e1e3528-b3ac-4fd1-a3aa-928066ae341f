/**
 * JavaScript للوحة الإدارة
 */

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

function initializeAdmin() {
    // تهيئة المكونات
    initializeSidebar();
    initializeSearch();
    initializeNotifications();
    initializeDataTables();
    initializeCharts();
    initializeTooltips();
    
}

// ========== الشريط الجانبي ==========
function initializeSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    const toggle = document.getElementById('sidebarToggle');
    const navLinks = document.querySelectorAll('.admin-nav-link');
    
    // تفعيل الرابط الحالي
    const currentPath = window.location.pathname;
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
    
    // إضافة تأثيرات التفاعل
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(-2px)';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

// ========== البحث العام ==========
function initializeSearch() {
    const searchInput = document.getElementById('globalSearch');
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            hideSearchResults();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            performGlobalSearch(query);
        }, 300);
    });
    
    // إخفاء النتائج عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!searchInput.contains(event.target)) {
            hideSearchResults();
        }
    });
}

function performGlobalSearch(query) {
    // إظهار مؤشر التحميل
    showSearchLoading();
    
    fetch(`/admin/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySearchResults(data.results);
            } else {
                showSearchError('حدث خطأ في البحث');
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showSearchError('حدث خطأ في الاتصال');
        });
}

function displaySearchResults(results) {
    // إنشاء قائمة النتائج
    const resultsContainer = createSearchResultsContainer();
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="search-no-results">لا توجد نتائج</div>';
        return;
    }
    
    const resultsList = results.map(result => `
        <a href="${result.url}" class="search-result-item">
            <div class="search-result-icon">
                <i class="${result.icon}"></i>
            </div>
            <div class="search-result-content">
                <div class="search-result-title">${result.title}</div>
                <div class="search-result-description">${result.description}</div>
            </div>
        </a>
    `).join('');
    
    resultsContainer.innerHTML = resultsList;
}

function createSearchResultsContainer() {
    let container = document.getElementById('searchResults');
    if (!container) {
        container = document.createElement('div');
        container.id = 'searchResults';
        container.className = 'search-results';
        document.getElementById('globalSearch').parentNode.appendChild(container);
    }
    return container;
}

function showSearchLoading() {
    const container = createSearchResultsContainer();
    container.innerHTML = '<div class="search-loading">جاري البحث...</div>';
    container.style.display = 'block';
}

function showSearchError(message) {
    const container = createSearchResultsContainer();
    container.innerHTML = `<div class="search-error">${message}</div>`;
}

function hideSearchResults() {
    const container = document.getElementById('searchResults');
    if (container) {
        container.style.display = 'none';
    }
}

// ========== الإشعارات ==========
function initializeNotifications() {
    loadNotifications();
    
    // تحديث الإشعارات كل 30 ثانية
    setInterval(loadNotifications, 30000);
}

function loadNotifications() {
    fetch('/admin/notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationBadges(data.counts);
                updateNotificationsList(data.notifications);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الإشعارات:', error);
        });
}

function updateNotificationBadges(counts) {
    // تحديث شارات العدد
    updateBadge('pendingStores', counts.pendingStores);
    updateBadge('newOrders', counts.newOrders);
    updateBadge('unreadNotifications', counts.unreadNotifications);
    updateBadge('headerNotificationCount', counts.unreadNotifications);
}

function updateBadge(elementId, count) {
    const badge = document.getElementById(elementId);
    if (!badge) return;
    
    if (count > 0) {
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = 'inline-block';
    } else {
        badge.style.display = 'none';
    }
}

function updateNotificationsList(notifications) {
    const container = document.getElementById('headerNotificationsList');
    if (!container) return;
    
    if (notifications.length === 0) {
        container.innerHTML = '<li class="dropdown-item text-muted text-center">لا توجد إشعارات جديدة</li>';
        return;
    }
    
    const notificationsList = notifications.slice(0, 5).map(notification => `
        <li>
            <a class="dropdown-item notification-item" href="${notification.url || '#'}">
                <div class="notification-icon ${notification.type}">
                    <i class="${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-time">${formatTime(notification.createdAt)}</div>
                </div>
            </a>
        </li>
    `).join('');
    
    container.innerHTML = notificationsList;
}

// ========== الجداول ==========
function initializeDataTables() {
    const tables = document.querySelectorAll('.admin-table');
    
    tables.forEach(table => {
        // إضافة فرز للأعمدة
        const headers = table.querySelectorAll('th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(table, header));
        });
        
        // إضافة تأثيرات التفاعل للصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'var(--bg-secondary)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
}

function sortTable(table, header) {
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const rows = Array.from(table.querySelectorAll('tbody tr'));
    const isAscending = header.classList.contains('sort-asc');
    
    // إزالة فئات الفرز من جميع الأعمدة
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // إضافة فئة الفرز للعمود الحالي
    header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
    
    // فرز الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        const comparison = aValue.localeCompare(bValue, 'ar', { numeric: true });
        return isAscending ? -comparison : comparison;
    });
    
    // إعادة ترتيب الصفوف
    const tbody = table.querySelector('tbody');
    rows.forEach(row => tbody.appendChild(row));
}

// ========== الرسوم البيانية ==========
function initializeCharts() {
    // رسم بياني للمبيعات
    const salesChart = document.getElementById('salesChart');
    if (salesChart) {
        createSalesChart(salesChart);
    }
    
    // رسم بياني للطلبات
    const ordersChart = document.getElementById('ordersChart');
    if (ordersChart) {
        createOrdersChart(ordersChart);
    }
}

function createSalesChart(canvas) {
    new Chart(canvas, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: 'var(--primary-color)',
                backgroundColor: 'var(--primary-lighter)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar') + ' ر.س';
                        }
                    }
                }
            }
        }
    });
}

function createOrdersChart(canvas) {
    new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: ['مكتملة', 'قيد التنفيذ', 'ملغية'],
            datasets: [{
                data: [65, 25, 10],
                backgroundColor: [
                    'var(--success-color)',
                    'var(--warning-color)',
                    'var(--danger-color)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// ========== التلميحات ==========
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltipElements.forEach(element => {
        new bootstrap.Tooltip(element);
    });
}

// ========== وظائف مساعدة ==========
function formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
}

function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(toast);
    
    // إزالة التلقائية بعد 5 ثوان
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// تصدير الوظائف للاستخدام العام
window.adminUtils = {
    showToast,
    formatTime,
    loadNotifications
};
