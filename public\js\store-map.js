let map;
let marker;

// تهيئة الخريطة
function initMap() {
    // إحداثيات الرياض كموقع افتراضي
    const defaultLat = 24.7136;
    const defaultLng = 46.6753;

    map = L.map('map').setView([defaultLat, defaultLng], 13);

    // إضافة طبقة الخريطة
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // إضافة marker قابل للسحب
    marker = L.marker([defaultLat, defaultLng], {
    draggable: true
    }).addTo(map);

    // تحديث الإحداثيات عند سحب الـ marker
    marker.on('dragend', function(e) {
    const position = marker.getLatLng();
    updateCoordinates(position.lat, position.lng);
    reverseGeocode(position.lat, position.lng);
    });

    // تحديث الموقع عند النقر على الخريطة
    map.on('click', function(e) {
    const lat = e.latlng.lat;
    const lng = e.latlng.lng;
    marker.setLatLng([lat, lng]);
    updateCoordinates(lat, lng);
    reverseGeocode(lat, lng);
    });
}

// تحديث حقول الإحداثيات
function updateCoordinates(lat, lng) {
    document.getElementById('latitude').value = lat.toFixed(6);
    document.getElementById('longitude').value = lng.toFixed(6);
}

// الحصول على الموقع الحالي
document.getElementById('getLocationBtn').addEventListener('click', function() {
    const btn = this;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
    btn.disabled = true;

    if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
        function(position) {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;

        // تحديث الخريطة والـ marker
        map.setView([lat, lng], 15);
        marker.setLatLng([lat, lng]);
        updateCoordinates(lat, lng);

        // الحصول على العنوان
        reverseGeocode(lat, lng);

        btn.innerHTML = originalText;
        btn.disabled = false;
        },
        function(error) {
        let errorMessage = 'Unable to get location. ';
        switch(error.code) {
            case error.PERMISSION_DENIED:
            errorMessage += 'Location access denied by user.';
            break;
            case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information unavailable.';
            break;
            case error.TIMEOUT:
            errorMessage += 'Location request timed out.';
            break;
        }

        alert(errorMessage);
        btn.innerHTML = originalText;
        btn.disabled = false;
        },
        {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
        }
    );
    } else {
    alert('Geolocation is not supported by this browser.');
    btn.innerHTML = originalText;
    btn.disabled = false;
    }
});

// تحويل الإحداثيات إلى عنوان
function reverseGeocode(lat, lng) {
     fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`)
    .then(response => response.json())
    .then(data => {
      if (data && data.address) {
        // جرب الحصول على اسم المنطقة من الحقول المحتملة
        const addr = data.address;
        const areaName = addr.suburb || addr.neighbourhood || addr.city_district || addr.district || addr.town || addr.city || '';

        // ضع المنطقة فقط في حقل العنوان أو في عنصر منفصل حسب حاجتك
        document.getElementById('address').value = data.display_name; // كامل العنوان
        // املأ الحقل المخفي فقط بالمنطقة
        document.getElementById('areaName').value = "Al-Mazzeh";//areaName;
        // لو تريد تعبئة حقل منطقة منفصل مثلا:
        // document.getElementById('areaNameField').value = areaName;
      }
    })
    .catch(error => {
      console.error('Error getting address:', error);
    });
}


// تهيئة الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initMap();
});
