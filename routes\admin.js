const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/AdminController');
const countriesController = require('../controllers/CountriesController');
const areasController = require('../controllers/AreasController');
const categoryController = require('../controllers/categoryController');
const customersController = require('../controllers/CustomersController');
const { requireAdminAuth } = require('./admin-auth');

// Apply authentication middleware to all admin routes
router.use(requireAdminAuth);

// Dashboard route
router.get('/', (req, res) => {
  res.redirect('dashboard');
});


router.get('/countries', countriesController.index.bind(countriesController));
router.get('/countries/create', countriesController.create.bind(countriesController));
router.post('/countries', countriesController.store.bind(countriesController));
router.get('/countries/:id', countriesController.show.bind(countriesController));
router.get('/countries/:id/edit', countriesController.edit.bind(countriesController));
router.post('/countries/:id', countriesController.update.bind(countriesController));
router.post('/:id/delete', countriesController.delete.bind(countriesController));



router.get('/areas', areasController.index.bind(areasController));
router.get('/areas/:id/stores', areasController.areaStores.bind(areasController));
router.get('/areas/:id/customers', areasController.areaCustomers.bind(areasController));
router.get('/areas/create', areasController.create.bind(areasController));
router.post('/areas', areasController.store.bind(areasController));
router.get('/areas/:id/edit', areasController.edit.bind(areasController));
router.post('/areas/:id', areasController.update.bind(areasController));
router.post('/areas/:id/delete', areasController.delete.bind(areasController));



router.get('/categories', categoryController.index);
router.get('/categories/create', categoryController.createForm);
router.post('/categories', categoryController.create);
router.get('/categories/:id/edit', categoryController.editForm);
router.post('/categories/:id', categoryController.update);
router.post('/categories/:id/delete', categoryController.delete);

// Admin stores routes
router.get('/dashboard', AdminController.dashboard.bind(AdminController));
router.get('/stores', AdminController.index.bind(AdminController));
router.get('/stores/create', AdminController.create.bind(AdminController));
router.post('/stores', AdminController.store.bind(AdminController));
router.get('/stores/:id/edit', AdminController.edit.bind(AdminController));
router.post('/stores/:id', AdminController.update.bind(AdminController));
router.post('/stores/:id/delete', AdminController.delete.bind(AdminController));
router.post('/stores/:id/status', AdminController.updateStatus.bind(AdminController));
router.get('/stores/:id/orders', AdminController.orders.bind(AdminController));
router.get('/orders', AdminController.show.bind(AdminController));
router.get('/orders/:id', AdminController.showDetail.bind(AdminController));
router.get('/stores/:id/drivers', AdminController.storeDrivers.bind(AdminController));
router.get('/stores/:storeId/drivers/:personId/deliveries', AdminController.personDeliveriesForStore.bind(AdminController));

router.get('/customers', customersController.showall.bind(customersController));
router.get('/customers/create', customersController.create.bind(customersController));
router.get('/customers/:id', customersController.show.bind(customersController));
router.post('/customers', customersController.store.bind(customersController));
router.get('/customers/:id/edit', customersController.edit.bind(customersController));
router.post('/customers/:id', customersController.update.bind(customersController));
router.post('/customers/:id/delete', customersController.delete.bind(customersController));
router.post('/customers/:id/status', customersController.updateStatus.bind(customersController));



module.exports = router;
