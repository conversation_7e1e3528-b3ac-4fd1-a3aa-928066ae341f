<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Store</h1>
        <a href="/admin/stores" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Stores
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="/admin/stores/<%= store.id %>" method="POST">
                <div class="mb-3">
                    <label for="name" class="form-label">Store Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="<%= store.name %>" required>
                </div>
                
                <div class="mb-3">
                    <label for="userName" class="form-label">Username</label>
                    <input type="text" class="form-control" id="userName" name="userName" value="<%= store.userName %>" required>
                </div>

                <!-- Multi-select Categories -->
                <div class="mb-3">
                    <label for="categoryIds" class="form-label">Categories</label>
                    <select class="form-select" id="categoryIds" name="categoryIds" multiple required>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.id %>"
                                <%= store.categories && store.categories.some(c => c.id === category.id) ? 'selected' : '' %>>
                                <%= category.name %>
                            </option>
                        <% }); %>
                    </select>
                    <small class="form-text text-muted">Hold Ctrl (Cmd on Mac) to select multiple</small>
                </div>

                <div class="mb-3">
                    <label for="areaId" class="form-label">Area</label>
                    <select class="form-select" id="areaId" name="areaId" required>
                        <option value="">Select Area</option>
                        <% areas.forEach(area => { %>
                            <option value="<%= area.id %>" <%= store.areaId === area.id ? 'selected' : '' %>>
                                <%= area.name %>
                            </option>
                        <% }); %>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="address" class="form-label">Address</label>
                    <div class="input-group">
                        <textarea class="form-control" id="address" name="address" rows="3"><%= store.address || '' %></textarea>
                        <button type="button" class="btn btn-outline-primary" id="getLocationBtn">
                            <i class="fas fa-map-marker-alt"></i> Get Location
                        </button>
                    </div>
                    <small class="form-text text-muted">Click "Get Location" to automatically fill address from your current location</small>
                </div>

                <!-- إحداثيات الموقع -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="latitude" class="form-label">Latitude</label>
                            <input type="number" step="any" class="form-control" id="latitude" name="latitude" value="<%= store.latitude || '' %>" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="longitude" class="form-label">Longitude</label>
                            <input type="number" step="any" class="form-control" id="longitude" name="longitude" value="<%= store.longitude || '' %>" readonly>
                        </div>
                    </div>
                </div>

                <!-- خريطة لعرض الموقع -->
                <div class="mb-3">
                    <label class="form-label">Store Location on Map</label>
                    <div id="map" style="height: 300px; border: 1px solid #ddd; border-radius: 5px;"></div>
                    <small class="form-text text-muted">The map shows the current store location. Click or drag to update.</small>
                </div>
                
                <div class="mb-3">
                    <label for="phoneNumber" class="form-label">Phone Number</label>
                    <input type="text" class="form-control" id="phoneNumber" name="phoneNumber" value="<%= store.phoneNumber || '' %>">
                </div>
                
                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="active" <%= store.status === 'active' ? 'selected' : '' %>>Active</option>
                        <option value="inactive" <%= store.status === 'inactive' ? 'selected' : '' %>>Inactive</option>
                        <option value="pending" <%= store.status === 'pending' ? 'selected' : '' %>>Pending</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><%= store.notes || '' %></textarea>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Update Store</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
    let map;
    let marker;

    // تهيئة الخريطة
    function initMap() {
        // الحصول على الإحداثيات الحالية أو استخدام الرياض كافتراضي
        const currentLat = parseFloat(document.getElementById('latitude').value) || 24.7136;
        const currentLng = parseFloat(document.getElementById('longitude').value) || 46.6753;

        map = L.map('map').setView([currentLat, currentLng], 13);

        // إضافة طبقة الخريطة
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // إضافة marker قابل للسحب
        marker = L.marker([currentLat, currentLng], {
            draggable: true
        }).addTo(map);

        // تحديث الإحداثيات عند سحب الـ marker
        marker.on('dragend', function(e) {
            const position = marker.getLatLng();
            updateCoordinates(position.lat, position.lng);
            reverseGeocode(position.lat, position.lng);
        });

        // تحديث الموقع عند النقر على الخريطة
        map.on('click', function(e) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            marker.setLatLng([lat, lng]);
            updateCoordinates(lat, lng);
            reverseGeocode(lat, lng);
        });
    }

    // تحديث حقول الإحداثيات
    function updateCoordinates(lat, lng) {
        document.getElementById('latitude').value = lat.toFixed(6);
        document.getElementById('longitude').value = lng.toFixed(6);
    }

    // الحصول على الموقع الحالي
    document.getElementById('getLocationBtn').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;

        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
        btn.disabled = true;

        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;

                    // تحديث الخريطة والـ marker
                    map.setView([lat, lng], 15);
                    marker.setLatLng([lat, lng]);
                    updateCoordinates(lat, lng);

                    // الحصول على العنوان
                    reverseGeocode(lat, lng);

                    btn.innerHTML = originalText;
                    btn.disabled = false;
                },
                function(error) {
                    let errorMessage = 'Unable to get location. ';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage += 'Location access denied by user.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage += 'Location information unavailable.';
                            break;
                        case error.TIMEOUT:
                            errorMessage += 'Location request timed out.';
                            break;
                    }

                    alert(errorMessage);
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        } else {
            alert('Geolocation is not supported by this browser.');
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    });

    // تحويل الإحداثيات إلى عنوان
    function reverseGeocode(lat, lng) {
        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`)
            .then(response => response.json())
            .then(data => {
                if (data && data.display_name) {
                    document.getElementById('address').value = data.display_name;
                }
            })
            .catch(error => {
                console.error('Error getting address:', error);
            });
    }

    // تهيئة الخريطة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initMap();
    });
</script>
