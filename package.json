{"name": "store-management-system", "version": "1.0.0", "description": "A professional store management system with modern architecture", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon -r dotenv/config app.js", "test": "jest --coverage", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix", "format": "prettier --write \"**/*.{js,ts,json,md}\"", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "migrate:undo:all": "sequelize-cli db:migrate:undo:all", "seed": "sequelize-cli db:seed:all", "prepare": "husky install"}, "keywords": ["store", "management", "inventory", "pos"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-flash": "^0.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "ejs": "^3.1.9", "express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mssql": "^9.1.1", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.7", "sequelize": "^6.35.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tedious": "^16.6.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.11", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.0.1", "prettier": "^3.2.4", "sequelize-cli": "^6.6.1", "supertest": "^6.3.4", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0"}}