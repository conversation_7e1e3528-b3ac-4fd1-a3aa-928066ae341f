# نظام التصميم الجديد - نظام إدارة المتاجر الذكي

## نظرة عامة

تم إعادة تصميم النظام بالكامل ليصبح أكثر احترافية وتناسقاً، مع التركيز على:
- نظام ألوان موحد من درجات الأخضر
- تصميم متجاوب ومتوافق مع جميع الأجهزة
- تجربة مستخدم محسنة
- أداء أفضل وتحميل أسرع

## نظام الألوان

### الألوان الأساسية
- **اللون الأساسي**: `#B2CD9C` (أخضر احترافي)
- **أخضر داكن**: `#8FB574`
- **أخضر فاتح**: `#C5D9B1`
- **أخضر فاتح جداً**: `#E8F0E3`

### تدرجات الأخضر
```css
--primary-50: #f0fdf4    /* أخضر فاتح جداً للخلفيات */
--primary-100: #dcfce7   /* أخضر فاتح للتمييز */
--primary-200: #bbf7d0   /* أخضر فاتح للحدود */
--primary-300: #86efac   /* أخضر متوسط فاتح */
--primary-400: #4ade80   /* أخضر متوسط */
--primary-500: #B2CD9C   /* اللون الأساسي */
--primary-600: #8FB574   /* أخضر داكن قليلاً */
--primary-700: #6B9D4F   /* أخضر داكن */
--primary-800: #4A7C32   /* أخضر داكن جداً */
--primary-900: #365F27   /* أخضر داكن للنصوص */
```

### ألوان الحالة
- **نجاح**: `#22c55e` (أخضر)
- **تحذير**: `#f59e0b` (برتقالي)
- **خطر**: `#ef4444` (أحمر)
- **معلومات**: `#3b82f6` (أزرق)

## المكونات الرئيسية

### الأزرار
```html
<!-- أزرار أساسية -->
<button class="btn btn-primary">أساسي</button>
<button class="btn btn-secondary">ثانوي</button>
<button class="btn btn-success">نجاح</button>
<button class="btn btn-warning">تحذير</button>
<button class="btn btn-danger">خطر</button>

<!-- أحجام مختلفة -->
<button class="btn btn-primary btn-sm">صغير</button>
<button class="btn btn-primary">عادي</button>
<button class="btn btn-primary btn-lg">كبير</button>
<button class="btn btn-primary btn-xl">كبير جداً</button>

<!-- أزرار الخطوط الخارجية -->
<button class="btn btn-outline-primary">خط خارجي</button>
<button class="btn btn-ghost">شبح</button>
```

### الكروت
```html
<!-- كرت أساسي -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">عنوان الكرت</h3>
    </div>
    <div class="card-body">
        <p class="card-text">محتوى الكرت</p>
    </div>
    <div class="card-footer">
        تذييل الكرت
    </div>
</div>

<!-- كرت إحصائيات -->
<div class="stats-card">
    <div class="stats-card-icon">
        <i class="fas fa-store"></i>
    </div>
    <div class="stats-card-value">245</div>
    <div class="stats-card-label">المتاجر المسجلة</div>
</div>
```

### التنبيهات
```html
<!-- تنبيهات مختلفة -->
<div class="alert alert-success">
    <div class="alert-icon">
        <i class="fas fa-check-circle"></i>
    </div>
    <div class="alert-content">
        <div class="alert-title">نجح!</div>
        تم حفظ البيانات بنجاح.
    </div>
</div>

<div class="alert alert-warning">
    <div class="alert-icon">
        <i class="fas fa-exclamation-triangle"></i>
    </div>
    <div class="alert-content">
        <div class="alert-title">تحذير!</div>
        يرجى مراجعة البيانات المدخلة.
    </div>
</div>
```

### النماذج
```html
<form>
    <div class="form-group">
        <label class="form-label required">الاسم الكامل</label>
        <input type="text" class="form-control" placeholder="أدخل اسمك الكامل">
    </div>
    
    <div class="form-group">
        <label class="form-label">البريد الإلكتروني</label>
        <input type="email" class="form-control" placeholder="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label class="form-label">نوع المستخدم</label>
        <select class="form-control form-select">
            <option>اختر نوع المستخدم</option>
            <option>عميل</option>
            <option>صاحب متجر</option>
        </select>
    </div>
    
    <button type="submit" class="btn btn-primary btn-block">
        <i class="fas fa-save me-2"></i>
        حفظ
    </button>
</form>
```

### الجداول
```html
<div class="table-container">
    <table class="table">
        <thead>
            <tr>
                <th class="sortable">الاسم</th>
                <th class="sortable">التاريخ</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>محمد أحمد</td>
                <td>2024-01-15</td>
                <td>
                    <button class="btn btn-sm btn-primary">تعديل</button>
                    <button class="btn btn-sm btn-danger">حذف</button>
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

## نظام الشبكة

```html
<div class="container">
    <div class="row">
        <div class="col-md-6">عمود نصف</div>
        <div class="col-md-6">عمود نصف</div>
    </div>
    
    <div class="row g-4">
        <div class="col-lg-3">ربع</div>
        <div class="col-lg-3">ربع</div>
        <div class="col-lg-3">ربع</div>
        <div class="col-lg-3">ربع</div>
    </div>
</div>
```

## الأدوات المساعدة

### المسافات
```html
<!-- الهوامش -->
<div class="m-0">بدون هامش</div>
<div class="m-4">هامش متوسط</div>
<div class="mt-3">هامش علوي</div>
<div class="mb-5">هامش سفلي</div>

<!-- الحشو -->
<div class="p-0">بدون حشو</div>
<div class="p-4">حشو متوسط</div>
<div class="px-3">حشو أفقي</div>
<div class="py-2">حشو عمودي</div>
```

### الألوان
```html
<!-- ألوان النص -->
<p class="text-primary">نص أساسي</p>
<p class="text-success">نص نجاح</p>
<p class="text-warning">نص تحذير</p>
<p class="text-danger">نص خطر</p>

<!-- ألوان الخلفية -->
<div class="bg-primary">خلفية أساسية</div>
<div class="bg-success-light">خلفية نجاح فاتحة</div>
```

### العرض والإخفاء
```html
<div class="d-none">مخفي</div>
<div class="d-block">ظاهر كبلوك</div>
<div class="d-flex">فليكس</div>
<div class="d-md-none">مخفي في الشاشات المتوسطة</div>
```

## الرسوم المتحركة

### فئات الحركة
```html
<div class="fade-in">ظهور تدريجي</div>
<div class="slide-in-right">انزلاق من اليمين</div>
<div class="slide-in-left">انزلاق من اليسار</div>
<div class="scale-in">تكبير تدريجي</div>
<div class="hover-lift">رفع عند التمرير</div>
<div class="hover-scale">تكبير عند التمرير</div>
```

## JavaScript API

### الإشعارات
```javascript
// إظهار إشعار
SmartStore.showNotification('تم الحفظ بنجاح!', 'success');
SmartStore.showNotification('حدث خطأ!', 'danger');

// رسالة تأكيد
SmartStore.confirm('هل أنت متأكد؟', function() {
    console.log('تم التأكيد');
});
```

### التنقل
```javascript
// إعادة تحميل الصفحة
SmartStore.reload();

// الانتقال لصفحة أخرى
SmartStore.redirect('/dashboard');
```

## الاستجابة للشاشات

### نقاط التوقف
- **الهواتف**: أقل من 640px
- **الأجهزة اللوحية**: 641px - 1024px
- **أجهزة الكمبيوتر**: أكبر من 1025px

### فئات الاستجابة
```html
<div class="d-sm-none">مخفي في الهواتف</div>
<div class="d-md-block">ظاهر في الأجهزة اللوحية فما فوق</div>
<div class="d-lg-flex">فليكس في أجهزة الكمبيوتر</div>
```

## الملفات المحدثة

1. **public/css/main.css** - نظام CSS الجديد الشامل
2. **views/layouts/main.ejs** - تخطيط محدث
3. **public/js/main.js** - JavaScript محسن
4. **views/demo.ejs** - صفحة عرض توضيحية

## التشغيل

لمشاهدة النظام الجديد:
1. تأكد من تشغيل الخادم
2. انتقل إلى `/demo` لمشاهدة صفحة العرض التوضيحية
3. استخدم الفئات الجديدة في صفحاتك

## الميزات الجديدة

- ✅ نظام ألوان موحد من درجات الأخضر
- ✅ تصميم متجاوب بالكامل
- ✅ رسوم متحركة سلسة
- ✅ تحسينات الأداء
- ✅ إمكانية الوصول المحسنة
- ✅ دعم الوضع المظلم (جاهز للتفعيل)
- ✅ تحسينات الطباعة
- ✅ تقليل الحركة للمستخدمين الذين يفضلون ذلك
