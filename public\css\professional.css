/**
 * نظام تصميم احترافي حقيقي
 * اللون الأساسي: #B2CD9C (أخضر فاتح)
 * تصميم متناسق ومتوازن مع قابلية قراءة ممتازة
 */

/* ========== نظام الألوان الاحترافي ========== */
:root {
  /* الألوان الأساسية - درجات الأخضر */
  --primary-50: #f0f9f0;
  --primary-100: #dcf2dc;
  --primary-200: #b8e5b8;
  --primary-300: #B2CD9C;  /* اللون الأساسي */
  --primary-400: #8fb574;
  --primary-500: #6b9d4f;
  --primary-600: #4a7c32;
  --primary-700: #3a5f27;
  --primary-800: #2d4a1f;
  --primary-900: #1f3316;

  /* الألوان المحايدة - درجات الرمادي */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-300: #d4d4d4;
  --gray-400: #a3a3a3;
  --gray-500: #737373;
  --gray-600: #525252;
  --gray-700: #404040;
  --gray-800: #262626;
  --gray-900: #171717;

  /* ألوان الحالة */
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --error: #ef4444;
  --error-light: #fee2e2;
  --info: #3b82f6;
  --info-light: #dbeafe;

  /* ألوان النصوص */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --text-inverse: #ffffff;

  /* ألوان الخلفيات */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: var(--gray-900);

  /* الحدود والظلال */
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);
  
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* المسافات */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */

  /* نصف الأقطار */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* الخطوط */
  --font-sans: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}

/* ========== إعادة تعيين أساسية ========== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  direction: rtl;
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========== العناوين ========== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.25;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* ========== الروابط ========== */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-300);
  outline-offset: 2px;
}

/* ========== الأزرار الاحترافية ========== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: 600;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.15s ease;
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--primary-300);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* الزر الأساسي */
.btn-primary {
  color: var(--text-inverse);
  background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-400) 100%);
  border-color: var(--primary-400);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-500) 100%);
  border-color: var(--primary-500);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  color: var(--text-inverse);
  text-decoration: none;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* الزر الثانوي */
.btn-secondary {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  color: var(--text-primary);
  text-decoration: none;
}

/* أزرار الحالة */
.btn-success {
  color: var(--text-inverse);
  background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
  border-color: var(--success);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  color: var(--text-inverse);
  text-decoration: none;
}

.btn-warning {
  color: var(--text-inverse);
  background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
  border-color: var(--warning);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  color: var(--text-inverse);
  text-decoration: none;
}

.btn-danger {
  color: var(--text-inverse);
  background: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
  border-color: var(--error);
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  color: var(--text-inverse);
  text-decoration: none;
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--font-size-xl);
}

/* أزرار مفرغة */
.btn-outline-primary {
  color: var(--primary-600);
  background-color: transparent;
  border-color: var(--primary-300);
}

.btn-outline-primary:hover {
  color: var(--text-inverse);
  background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-400) 100%);
  border-color: var(--primary-400);
  text-decoration: none;
}

/* ========== البطاقات الاحترافية ========== */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--primary-200);
}

.card-header {
  padding: var(--space-6);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* بطاقات الإحصائيات */
.stats-card {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--primary-50) 100%);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, var(--primary-100) 0%, transparent 70%);
  opacity: 0.5;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-300);
}

.stats-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-4);
  background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-400) 100%);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: var(--font-size-2xl);
  box-shadow: var(--shadow-md);
}

.stats-value {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--primary-700);
  margin-bottom: var(--space-2);
  line-height: 1;
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-change {
  margin-top: var(--space-3);
  font-size: var(--font-size-sm);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
}

.stats-change.positive {
  color: var(--success);
}

.stats-change.negative {
  color: var(--error);
}

.stats-change.neutral {
  color: var(--text-muted);
}

/* ========== النماذج الاحترافية ========== */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-300);
  box-shadow: 0 0 0 4px var(--primary-100);
  background-color: var(--primary-50);
}

.form-control:disabled {
  background-color: var(--bg-tertiary);
  border-color: var(--border-light);
  color: var(--text-muted);
  cursor: not-allowed;
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-text {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--space-1);
}

/* ========== التنبيهات الاحترافية ========== */
.alert {
  padding: var(--space-4) var(--space-6);
  margin-bottom: var(--space-6);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.alert-success {
  color: #065f46;
  background: linear-gradient(135deg, var(--success-light) 0%, #ecfdf5 100%);
  border-color: #a7f3d0;
}

.alert-warning {
  color: #92400e;
  background: linear-gradient(135deg, var(--warning-light) 0%, #fffbeb 100%);
  border-color: #fde68a;
}

.alert-danger {
  color: #991b1b;
  background: linear-gradient(135deg, var(--error-light) 0%, #fef2f2 100%);
  border-color: #fecaca;
}

.alert-info {
  color: #1e40af;
  background: linear-gradient(135deg, var(--info-light) 0%, #eff6ff 100%);
  border-color: #93c5fd;
}

/* ========== الجداول الاحترافية ========== */
.table-container {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.table th {
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
  padding: var(--space-4) var(--space-6);
  text-align: right;
  font-weight: 700;
  color: var(--primary-800);
  border-bottom: 2px solid var(--primary-300);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
  color: var(--text-primary);
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--primary-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* شارات الحالة */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-radius: var(--radius-full);
  border: 1px solid transparent;
}

.badge-success {
  color: #065f46;
  background-color: var(--success-light);
  border-color: #a7f3d0;
}

.badge-warning {
  color: #92400e;
  background-color: var(--warning-light);
  border-color: #fde68a;
}

.badge-danger {
  color: #991b1b;
  background-color: var(--error-light);
  border-color: #fecaca;
}

.badge-primary {
  color: var(--primary-800);
  background-color: var(--primary-100);
  border-color: var(--primary-200);
}

/* ========== الشريط العلوي الاحترافي ========== */
.navbar-professional {
  background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-400) 100%);
  border-bottom: 3px solid var(--primary-500);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4) 0;
}

.navbar-brand-professional {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--text-inverse);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.navbar-brand-professional:hover {
  color: var(--text-inverse);
  text-decoration: none;
  transform: scale(1.02);
}

.navbar-nav-professional .nav-link {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  margin: 0 var(--space-1);
}

.navbar-nav-professional .nav-link:hover,
.navbar-nav-professional .nav-link.active {
  color: var(--text-inverse);
  background-color: rgba(255, 255, 255, 0.15);
  text-decoration: none;
  transform: translateY(-1px);
}

/* ========== الشبكة والتخطيط ========== */
.container-professional {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.row-professional {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--space-3));
}

.col-professional {
  flex: 1;
  padding: 0 var(--space-3);
}

/* أعمدة محددة */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ========== الأدوات المساعدة ========== */
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-left { text-align: left !important; }

.text-primary { color: var(--primary-600) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--error) !important; }

.bg-primary { background-color: var(--primary-300) !important; }
.bg-light { background-color: var(--bg-secondary) !important; }
.bg-white { background-color: var(--bg-primary) !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }

.mt-4 { margin-top: var(--space-4) !important; }
.mt-6 { margin-top: var(--space-6) !important; }

.p-4 { padding: var(--space-4) !important; }
.p-6 { padding: var(--space-6) !important; }

.rounded { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ========== الاستجابة للشاشات ========== */
@media (max-width: 768px) {
  .container-professional {
    padding: 0 var(--space-4);
  }

  .row-professional {
    margin: 0 calc(-1 * var(--space-2));
  }

  .col-professional {
    padding: 0 var(--space-2);
  }

  .btn {
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-sm);
  }

  .card-body {
    padding: var(--space-4);
  }

  .stats-card {
    padding: var(--space-6);
  }

  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
}

/* ========== تحسينات إضافية ========== */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* تحسين التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-400);
}
