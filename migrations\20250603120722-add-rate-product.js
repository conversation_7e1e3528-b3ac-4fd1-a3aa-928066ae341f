'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Products', 'rating', {
      type: Sequelize.STRING,
      allowNull: true
    });

    await queryInterface.removeColumn('Products', 'discountStartDate');
    await queryInterface.removeColumn('Products', 'discountEndDate');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Products', 'rating');
    await queryInterface.addColumn('Products', 'discountStartDate', {
      type: Sequelize.DATE,
      allowNull: true
    });
    await queryInterface.addColumn('Products', 'discountEndDate', {
      type: Sequelize.DATE,
      allowNull: true
    });
  }
};
