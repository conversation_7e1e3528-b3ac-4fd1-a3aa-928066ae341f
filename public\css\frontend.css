/**
 * تنسيق الواجهة الأمامية والعملاء
 */

/* ========== الشريط العلوي ========== */
.main-navbar {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-white);
  padding: var(--spacing-md) 0;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-brand {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--text-white);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.navbar-brand:hover {
  color: var(--text-white);
  text-decoration: none;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  list-style: none;
}

.navbar-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.navbar-link:hover,
.navbar-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  text-decoration: none;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.cart-icon {
  position: relative;
  color: var(--text-white);
  font-size: var(--font-size-xl);
  text-decoration: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.cart-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  text-decoration: none;
}

.cart-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  background-color: var(--danger-color);
  color: var(--text-white);
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* ========== البانر الرئيسي ========== */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-primary);
  padding: var(--spacing-2xl) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-2xl);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

.btn-hero {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
  border-radius: var(--radius-xl);
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-hero-primary {
  background-color: var(--text-white);
  color: var(--primary-dark);
  border: 2px solid var(--text-white);
}

.btn-hero-primary:hover {
  background-color: var(--primary-dark);
  color: var(--text-white);
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-hero-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border: 2px solid var(--text-primary);
}

.btn-hero-secondary:hover {
  background-color: var(--text-primary);
  color: var(--text-white);
  text-decoration: none;
  transform: translateY(-2px);
}

/* ========== بطاقات المنتجات ========== */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.product-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background-color: var(--bg-tertiary);
  position: relative;
}

.product-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background-color: var(--danger-color);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.product-content {
  padding: var(--spacing-lg);
}

.product-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  line-height: 1.3;
}

.product-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.product-price {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.product-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--warning-color);
  font-size: var(--font-size-sm);
}

.product-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-add-cart {
  flex: 1;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.btn-add-cart:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.btn-wishlist {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.btn-wishlist:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* ========== بطاقات المتاجر ========== */
.stores-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.store-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.store-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.store-header {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-color);
}

.store-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.store-avatar {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-xl);
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.store-details h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.store-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--warning-color);
  font-size: var(--font-size-sm);
}

.store-body {
  padding: var(--spacing-lg);
}

.store-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.store-stat {
  text-align: center;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.store-stat-value {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.store-stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ========== السلة ========== */
.cart-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
}

.cart-item-image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-md);
  object-fit: cover;
  background-color: var(--bg-tertiary);
}

.cart-item-details {
  flex: 1;
}

.cart-item-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.cart-item-price {
  font-size: var(--font-size-md);
  color: var(--primary-color);
  font-weight: 600;
}

.cart-item-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs);
}

.quantity-btn {
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.quantity-btn:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.quantity-input {
  width: 60px;
  text-align: center;
  border: none;
  background-color: transparent;
  font-weight: 600;
  color: var(--text-primary);
}

/* ========== الاستجابة للشاشات ========== */
@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn-hero {
    width: 100%;
    max-width: 300px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
  }
  
  .stores-grid {
    grid-template-columns: 1fr;
  }
  
  .cart-item {
    flex-direction: column;
    text-align: center;
  }
  
  .cart-item-actions {
    width: 100%;
    justify-content: center;
  }
}
