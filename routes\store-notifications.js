const express = require('express');
const router = express.Router();
const storeNotificationsController = require('../controllers/StoreNotificationsController');
const { auth } = require('../middleware/auth');

// تطبيق middleware على جميع الطرق
router.use(auth.store);

// Web Routes
router.get('/', storeNotificationsController.index.bind(storeNotificationsController));
router.get('/:id', storeNotificationsController.show.bind(storeNotificationsController));
router.post('/:id/read', storeNotificationsController.markAsRead.bind(storeNotificationsController));
router.post('/mark-all-read', storeNotificationsController.markAllAsRead.bind(storeNotificationsController));
router.post('/:id/delete', storeNotificationsController.delete.bind(storeNotificationsController));

// API Routes
router.get('/unread-count', storeNotificationsController.getUnreadCount.bind(storeNotificationsController));
router.get('/recent', storeNotificationsController.getRecentNotifications.bind(storeNotificationsController));

module.exports = router;
